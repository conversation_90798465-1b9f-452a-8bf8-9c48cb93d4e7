#include "OW_DS1820.h"
#include "console.h"
#include <stdlib.h>

#include "temperature.h"

static int16_t s_temperature = 0;

const int16_t *temperature_get(void)
{
    ds1820_ReadTempC(&s_temperature);

    return &s_temperature;
}

const char *temperature_get_string(void)
{
    static char temperature_string[16];

    snprintf(temperature_string, sizeof(temperature_string), "%d.%d", s_temperature/10, abs(s_temperature)%10);

    return temperature_string;
}