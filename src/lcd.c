/*------------------------------------------------------------------------------
Company         : TXD Systems
Module          : lcd.c
Project			: LCD Controller
Purpose         : Driver interface to the LCD
Prefix          : lcd_
Author			: 
History         :
Date
dd/mm/yyyy  Author   Description
----------  ------   -----------
--------------------------------------------------------------------------------
Instruction Register description:
---------------------------------
Function set:
RS R/W DB7 DB6 DB5 DB4		DB3 DB2 DB1 DB0
 0	 0	  0	0	 1	 DL		 N 	F	�	 �
DL = 1 Data 8-bit lengths(DB7 to DB0) when
DL = 0 Data 4-bit lengths(DB7 to DB4) NOTE! data must be sent or received twice.
N = 1: 2 lines,		N = 0: 1 line.
F = 1: 5x10 dots,	F = 0: 5x8 dots(character font).
37 ms

Display on/off control:
RS R/W DB7 DB6 DB5 DB4 DB3 DB2 DB1 DB0
0	0	 0	  0	0	  0	1	 D	 C	  B
D: The display is ON when D = 1 & off when D is 0.
C: The cursor is ON when C = 1 & OFF when C = 0. E
B: The character indicated by the cursor blinks when B = 1.
37 ms

Entry  mode set:
RS R/W DB7 DB6 DB5 DB4 DB3 DB2 DB1 DB0
0	0	0	0	0	0	0	1	I/D  S
		0	0	0	0	0	1	1	1	- increments, shift right
I/D: Increments(I/D = 1) or decrements(I/D = 0)
S = 1: Shifts the entire display to the right(I/D = 1) or to the left(I/D = 0). S = 0: NO shift.
37 ms

Cursor or display  shift:
RS R/W DB7 DB6 DB5 DB4 DB3 DB2 DB1 DB0
0	0	0	0	0	1	S/C R/L	 �	 � 
S/C = 1: Shifts display S/C = 0 Moves cursor(without changing  DDRAM contents.)
R/L = 1: Move right,  R/L = 0: Move left 
S/C R/L
0	0	Shifts the cursor position to the left.(AC is decremented by one.)
0	1	Shifts the cursor position to the right.(AC is incremented by one.)
1	0	Shifts the entire display to the left. The cursor follows the display shift.
1	1	Shifts the entire display to the right. The cursor follows the display shift.
------------------------------------------------------------------------------*/

/*------------------------------------------------------------------------------
                                      INCLUDES
------------------------------------------------------------------------------*/
#include <avr/io.h>
#include <stdlib.h>
#include "types.h"
#include "system.h"
#include "pins.h"
#include "lcd_def.h"
#include "delay.h"
#include "io_pins.h"
#include "console.h"
#include "lcd.h"

#define CGRAMWR 0x40
#define CTRL_PORT PORTC
#define DATA_PORT PORTA
#define EN_PORT PORTB  

//void DISPLAY_TEST(void) ;
/*------------------------------------------------------------------------------
                                LOCAL VARIABLES
------------------------------------------------------------------------------*/
static uint8_t  lcd_x;                    /*LCD x position*/
static uint8_t  lcd_y;                    /*LCD y position*/
static bool lcd_blink_state     = false;/*Assume the Blink is off at startup*/
static bool lcd_backlight_state = false;/*Assume the Backlight is off at startup*/

/*------------------------------------------------------------------------------
 Function   : lcd_setup
 Purpose    : Initialize the LCD Display. Set all the variables to default values
              as in config file
 Parameters : None
 Returns    : Nothing
 Notes      : Step Instruction 4 bit MODE:(NOTE - two writes each time)

 1. Power supply ON(the HD44780U is initialized by the internal reset circuit) - No display.

 2. Function set
 RS R/W DB7 DB6 DB5 DB4
 0	0	0	0	1	0	= Sets to 4-bit operation.

 3. Function set
 RS R/W DB7 DB6 DB5 DB4
 0	0	0	0	1	0  =  Sets 4-bit operation & selects 1-line display and 5 x 8 dot character font.
 0	0	0	0	*	*  =  4-bit operation starts from this step & resetting is necessary.(Number of display lines and
							character fonts cannot be changed after step #3.)
Function set:
RS		DB7 DB6 DB5 DB4		DB3 DB2 DB1 DB0
0		0	0	1	DL		N	RE	DH	RE
0		0	0	1	0		1	0	0	1	4-bit, 2 line, 5x8 dot
DL = 1 Data 8-bit lengths(DB7 to DB0) when
DL = 0 Data 4-bit lengths(DB7 to DB4) NOTE! data must be sent or received twice.
N = 1: 2 lines,		N = 0: 1 line.
F = 1: 5x10 dots,	F = 0: 5x8 dots(character font).
RE= 0/1 : extension register 
scroll/shift(DH=0: dot scroll; DH=1: display shift)
reverse bit(REV=0:normal; REV=1:inverse display)

Extended Function Set
RE RS	DB7 DB6 DB5 DB4		DB3 DB2 DB1 DB0
1	0	0	0	 0	0		1	FW	BW	NW
FW=0: 5-dot font width; FW=1: 6-dot font width
BW=0: normal cursor; BW=1: inverting cursor
NW=0: 1- or 2-line(see N); NW=1: 4-line display


 4. Display on/off control:
 RS DB7 DB6 DB5 DB4
 0		0	0	0	0	= Turns on display and cursor. Entire display is in space mode because of initialization.
 0		1	D	C	B
 0		1	1	1	1 = Display ON, Cursor ON, Blink ON
 D: 0 - Display off
 C: 0 -	Cursor off
 B: 0 - Blinking off

 5. Cursor mode set:
 RS		DB7 DB6 DB5 DB4
 0		0	0	0	1	
		DB3 DB2 DB1 DB0
 0		S/C	R/L	0	0
 0		0	1	0	0     
 S/C R/L
 0	 0 Shifts cursor position left.(AC is decremented by one.)
 0	 1 Shifts cursor position right.(AC is incremented by one.)
 1	 0 Shifts entire display  left. The cursor follows the display shift.
 1	 1 Shifts entire display  right. The cursor follows the display shift.

 6. Entry mode set:
 RS	DB7 DB6 DB5 DB4
 0		0	0	0	0	= Sets mode to increment the address by one and to shift the cursor to the right at the time of
 0		0	1	I/D	S
 0		0	1	1	0     write to the DD/CGRAM. Display is not shifted.
 I/D: 1 - Increment by 1. 0 - Decrement by 1.
 S:	  0 - No shift

 6. Write data to CGRAM/DDRAM:
 RS R/W DB7 DB6 DB5 DB4
 1	0	0	1	0	0 - Writes 'H_'. The cursor is incremented by one and shifts to the right.
 1	0	1	0	0	0
 
------------------------------------------------------------------------------*/
void lcd_setup(void)
{
	uint8_t dly = 1;

	/* ----------------------------------------------------
		If DDRxn is written to '0', the Pin is configured as 
	   an input.
	------------------------------------------------------*/
	DDRA |= 0x0F ;				// 0000 1111 - set LCD data port WR Direction. TMS 2.1
	DDRC |= 0xE0 ;				// 1110 0000 - set LCD port WR Direction. TMS 2.1
	DDRB |= 0x01 ;				// set LCD port EN Direction.


	EN_PORT &= 0xFE ;		/* Set LCD Enable = "0"  */
	delay_ms(dly);
	DATA_PORT = bit_8 ;			/* 1. Function set to 4-bit msb x1 */
	for(uint8_t i = 0; i<3; i++)
	{	
		delay_ms(dly)	;
		EN_PORT |= 0x01 ;			/* Set LCD Enable = "1"  */
		delay_ms(dly);
		EN_PORT &= 0xFE ;			/* Set LCD Enable = "0"  */
	}
	/*-------Configure-for-4-bit-bus-operation----------------------*/	
	DATA_PORT = bit_4 ;			/* 3a. Function set to 4-bit msb x4*/
	delay_ms(1)	;
	EN_PORT |= 0x01 ;			/* Set LCD Enable = "1"  */
	delay_ms(dly);
	EN_PORT &= 0xFE ;			/* Set LCD Enable = "0"  */
	delay_ms(dly);

	/*--------Function set-bit------------------------------------*/
	DATA_PORT = bit_4 ;			/* 3a. Set Display Font msb */
	delay_ms(1)	;
	EN_PORT |= 0x01 ;			/* Set LCD Enable = "1"  */
	delay_ms(dly);
	EN_PORT &= 0xFE ;			/* Set LCD Enable = "0"  */
	delay_ms(dly);

	DATA_PORT = 0x08 ;		/* 3b. Function set to 4-bit lsb x2*/
	delay_ms(1)	;
	EN_PORT |= 0x01 ;			/* Set LCD Enable = "1"  */
	delay_ms(dly);
	EN_PORT &= 0xFE ;			/* Set LCD Enable = "0"  */
	delay_ms(dly);
	/*------------Display-On\off-Cursor on\off-Blink on\off---*/
	DATA_PORT = 0x00 ;			/* 4a. msb */
	delay_ms(1)	;
	EN_PORT |= 0x01 ;			/* Set LCD Enable = "1"  */
	delay_ms(dly);
	EN_PORT &= 0xFE ;			/* Set LCD Enable = "0"  */
	delay_ms(dly);

	DATA_PORT = 0x0C ;		/* 4b. lsb Display=ON; Cursor & Blink = OFF */
	//DATA_PORT = 0x08 ;
	delay_ms(1)	;
	EN_PORT |= 0x01 ;			/* Set LCD Enable = "1"  */
	delay_ms(dly);
	EN_PORT &= 0xFE ;			/* Set LCD Enable = "0"  */
	delay_ms(dly);
	/*------------Entry mode-set------------------------------*/
	DATA_PORT = 0x00 ;			/* 5a. msb */
	delay_ms(1)	;
	EN_PORT |= 0x01 ;			/* Set LCD Enable = "1"  */
	delay_ms(dly);
	EN_PORT &= 0xFE ;			/* Set LCD Enable = "0"  */
	delay_ms(dly);

	DATA_PORT = 0x06 ;			/* 5b. lsb */
	delay_ms(1)	;
	EN_PORT |= 0x01 ;			/* Set LCD Enable = "1"  */
	delay_ms(dly);
	EN_PORT &= 0xFE ;			/* Set LCD Enable = "0"  */
	delay_ms(dly);
}

void lcd_init(void)
{
	lcd_on(true);
	lcd_setup();
	lcd_custom_char_init(); /* Create special custom character in CGRAM */
}

void lcd_parallel_write(int8_t data, uint8_t RS)
{
	uint8_t portout = 0, msb_data = 0, lsb_data = 0 ;
	uint8_t dly = 1;	// was "1"

	DDRA |= 0x0F ;			/* Set data bit direction  */
	PINB &= 0xFE ;			/* Clear EN  */
	delay_ms(dly);
	
	msb_data = data & 0xF0 ;	/* Mask out Msb's */
	msb_data = msb_data >> 4 ; /* shift msb to Lsb position */
	lsb_data = data & 0x0F ;	/* Mask out lsb's */	
	
	PINB &= 0xFE ;	
	delay_ms(dly);	

	/* Set for Data reg RS = '1' for data transfer or '0' for Command
		Configure for Write operation.
	-----------------------------------------------------------------*/
	if (RS)	/* Data WR */
	{	
		portout |= LCD_RS  ;		/* RS = 1, R/W = 0 - Command */
		portout &= ~LCD_WR ; 
		CTRL_PORT = portout ;
	}  /* RS = 1 */
	else		/* Command(instruction) WR */
	{		
		/* RS, R/W = 0 - Command */	
		CTRL_PORT &= 0x9F ;			/* TMS 2.0*/
	
	} 	/* RS, R/W = 0 */	

	/* MSB Write instruction to data bus 
	-------------------------------------*/	
	portout &= 0xF0  ;		/* Clear lsb */
	portout |= msb_data  ;
	DATA_PORT = portout ;
	delay_ms(1)	;

	/*				/������������������
	EN  ______ /						*/		 
	EN_PORT |= LCD_EN ;	/* Set LCD Enable  */
	delay_ms(dly);
					
			
	/*	 ������������������\
	EN		   				  \________*/
	
	EN_PORT &= 0xFE ;			/* Clear LCD Enable to latch data */
	delay_ms(dly);

	/* LSB Write instruction to databus 
	------------------------------------*/		
	portout &= 0xF0  ;		/* Clear lsb */
	portout |= lsb_data  ;
	DATA_PORT = portout ;
	delay_ms(1)	;

	/*				/������������������
	EN  ______ /						*/		 
	EN_PORT |= LCD_EN ;	/* Set LCD Enable "data + 110x" */
	delay_ms(dly);
		
			
	/*	 ������������������\
	EN  				        \________*/
	EN_PORT &= 0xFE ;			/* Clear LCD Enable to latch data */
	delay_ms(dly);

	return ;
}

void lcd_display_string(char *strPtr, uint8_t x, uint8_t y)
{
	unsigned char cSREG;

	cSREG = SREG;	
	asm("cli");			

	lcd_cursor_set_pos(x, y);

	while ((x++ < LCD_TOTAL_COL) &&(*strPtr != '\0'))
	{
		lcd_parallel_write(*strPtr, RS_Data) ;
		++strPtr;
	}
	lcd_x = x ;
	lcd_y = y ;
	lcd_cursor_set_pos(x, y);

	SREG = cSREG;
}

void lcd_clear(void)
{
   lcd_cmd(LCD_CLR);
   lcd_cursor_set_pos(0,0);
}

void lcd_display(bool State)
{
	if (State == 1)
	{
		lcd_cmd(LCD_DISP_ON);       
	}
	else
	{
		lcd_cmd(LCD_DISP_OFF);  
	}
}

void lcd_home(void)
{
	lcd_cmd(LCD_HOME);     //Difference between LCD_HOME and setting cursor(0,0)??
    lcd_cursor_set_pos(0,0); 
}

void lcd_cursor(bool state)
{
	lcd_cmd(state ? LCD_CURS_ON : LCD_CURS_OFF);
}

bool lcd_blink_on(void)
{
   return lcd_blink_state;
}

void lcd_blink(bool state)
{
	lcd_cmd(state ? LCD_BLINK_ON : LCD_BLINK_OFF);
	lcd_blink_state = state;
}

bool lcd_backlight_on(void)
{
   return lcd_backlight_state;
}

void lcd_backlight(bool state)
{
   if (state)
	   BL_PORT |= (1 << BL_PIN);
   else
	   BL_PORT &= ~(1 << BL_PIN);

   lcd_backlight_state = state;
}

void lcd_cursor_set_pos(uint8_t x, uint8_t y)
{
   uint8_t cursor;   /* DDRAM address value for cursor */
	
   switch(y)    /* Set the row address */
   {
		case 0x00:
			cursor = LCD_ROW1;
			break;

		case 0x01:
			cursor = LCD_ROW2;
			break;

		case 0x02:
			cursor = LCD_ROW3;
			break;

		case 0x03:
			cursor = LCD_ROW4;
			break;

		default:
			cursor = LCD_ROW1;
   }

   cursor += x;            /* Add the column address to row address */
   cursor |= 0x80;         /* Set bit 7 to change DDRAM*/
   lcd_cmd(cursor);        /* Update DDRAM address to change cursor */  

	lcd_x = x;
	lcd_y = y;

   return;
}


void lcd_cursor_get_pos(uint8_t *x, uint8_t *y)
{
   *x = lcd_x;
   *y = lcd_y;
   return;
}

void lcd_custom_char_write(unsigned char loc, unsigned char *data)
{
    unsigned char i;
    
	if (loc < 8)
    {
		lcd_cmd(CGRAMWR +(loc << 3));  /* Command 0x40 and onwards forces */
                                  /* the device to point CGRAM address */
		for(i=0;i<8;i++)  /* Write char rows to CGRAM */
			lcd_data(data[i]);
   }   
}

void lcd_custom_char_init()
{
	unsigned char Celcius[8] = { 0x18, 0x18, 0x0E, 0x11, 0x10, 0x11, 0x0E, 0x00 };
	unsigned char Sig_1[8] = { 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0x00 };
	unsigned char Sig_2[8] = { 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0x1F, 0x00 };
	unsigned char Sig_3[8] = { 0x00, 0x00, 0x00, 0x00, 0x1F, 0x1F, 0x1F, 0x00 };
	unsigned char Sig_4[8] = { 0x00, 0x00, 0x00,0x1F, 0x1F, 0x1F, 0x1F, 0x00 };
	unsigned char Sig_5[8] = { 0x00, 0x00, 0x1F, 0x1F, 0x1F, 0x1F, 0x1F, 0x00 };
	unsigned char Sig_6[8] = { 0x00, 0x1F, 0x1F,0x1F, 0x1F, 0x1F, 0x1F, 0x00 };
	unsigned char Sig_7[8] = { 0x1F, 0x1F, 0x1F, 0x1F, 0x1F, 0x1F, 0x1F, 0x00 };	
		
	lcd_custom_char_write(CELCIUS_C, Celcius);			/*Write pattern to CGRAM data addr 0x00 */
	lcd_custom_char_write(SIG_1, Sig_1);				/* Build Character9 at position 8 */
	lcd_custom_char_write(SIG_2, Sig_2);				/* Build Character10 at position 9 */
	lcd_custom_char_write(SIG_3, Sig_3);				/* Build Character11 at position 10 */
	lcd_custom_char_write(SIG_4, Sig_4);				/* Build Character12 at position 11 */
	lcd_custom_char_write(SIG_5, Sig_5);				/* Build Character13 at position 12 */
	lcd_custom_char_write(SIG_6, Sig_6);				/* Build Character14 at position 13 */
	lcd_custom_char_write(SIG_7, Sig_7);				/* Build Character15 at position 14 */
}

void lcd_custom_char_display(char ch, uint8_t x, uint8_t y) 
{
	lcd_cursor_set_pos(x, y) ;
	lcd_data(ch);
}

void lcd_cmd(uint8_t cmd)
{
	lcd_parallel_write(cmd, 0) ;		/* RS = CMD Register(0), 4bit */	
}

void lcd_data(uint8_t data)
{
	lcd_parallel_write(data, 1) ;		/* RS = DATA Register(1) */
}

void lcd_on(bool state)
{
	PinWR(LCD_SW, &PORTC, state ? 0 : 1) ; // Set LCD power on or off
}

void lcd_write(uint8_t x, uint8_t y, const char *format, ...)
{
    char buffer[LCD_LINE_LENGTH + 1];
    va_list args;
    va_start(args, format);
    vsnprintf(buffer, sizeof(buffer)-x, format, args);
    va_end(args);

	lcd_display_string(buffer, x, y);
}

void lcd_writeline(uint8_t y, const char *format, ...)
{
	char buffer[LCD_LINE_LENGTH + 1];
	va_list args;
	va_start(args, format);
	int chars = vsnprintf(buffer, sizeof(buffer), format, args);
	va_end(args);

	while (chars < LCD_LINE_LENGTH) {
		buffer[chars++] = ' ';
	}
	buffer[LCD_LINE_LENGTH] = '\0';

	lcd_display_string(buffer, 0, y);
}