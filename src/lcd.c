/*------------------------------------------------------------------------------
Company         : TXD Systems
Module          : lcd.c
Project			: LCD Controller
Purpose         : Driver interface to the LCD
Prefix          : lcd_
Author			: 
History         :
Date
dd/mm/yyyy  Author   Description
----------  ------   -----------
--------------------------------------------------------------------------------
Instruction Register description:
---------------------------------
Function set:
RS R/W DB7 DB6 DB5 DB4		DB3 DB2 DB1 DB0
 0	 0	  0	0	 1	 DL		 N 	F	�	 �
DL = 1 Data 8-bit lengths (DB7 to DB0) when
DL = 0 Data 4-bit lengths (DB7 to DB4) NOTE! data must be sent or received twice.
N = 1: 2 lines,		N = 0: 1 line.
F = 1: 5x10 dots,	F = 0: 5x8 dots (character font).
37 ms

Display on/off control:
RS R/W DB7 DB6 DB5 DB4 DB3 DB2 DB1 DB0
0	0	 0	  0	0	  0	1	 D	 C	  B
D: The display is ON when D = 1 & off when D is 0.
C: The cursor is ON when C = 1 & OFF when C = 0. E
B: The character indicated by the cursor blinks when B = 1.
37 ms

Entry  mode set:
RS R/W DB7 DB6 DB5 DB4 DB3 DB2 DB1 DB0
0	0	0	0	0	0	0	1	I/D  S
		0	0	0	0	0	1	1	1	- increments, shift right
I/D: Increments (I/D = 1) or decrements (I/D = 0)
S = 1: Shifts the entire display to the right (I/D = 1) or to the left (I/D = 0). S = 0: NO shift.
37 ms

Cursor or display  shift:
RS R/W DB7 DB6 DB5 DB4 DB3 DB2 DB1 DB0
0	0	0	0	0	1	S/C R/L	 �	 � 
S/C = 1: Shifts display S/C = 0 Moves cursor (without changing  DDRAM contents.)
R/L = 1: Move right,  R/L = 0: Move left 
S/C R/L
0	0	Shifts the cursor position to the left. (AC is decremented by one.)
0	1	Shifts the cursor position to the right. (AC is incremented by one.)
1	0	Shifts the entire display to the left. The cursor follows the display shift.
1	1	Shifts the entire display to the right. The cursor follows the display shift.
------------------------------------------------------------------------------*/

/*------------------------------------------------------------------------------
                                      INCLUDES
------------------------------------------------------------------------------*/
#include <avr/io.h>
#include <stdlib.h>
#include "types.h"
#include "system.h"
#include "pins.h"
#include "lcd_def.h"
#include "delay.h"
#include "io_pins.h"
#include "console.h"

#define CGRAMWR 0x40
#define CTRL_PORT PORTC
#define DATA_PORT PORTA
#define EN_PORT PORTB  
/*------------------------------------------------------------------------------
                          LOCAL FUNCTION PROTOTYPES
------------------------------------------------------------------------------*/
static void lcd_Cmd              (UI_8 cmd);
static void lcd_Data             (UI_8 data);
void   lcd_Init                (void);
void   lcd_Clear               (void);
void   lcd_Home                (void);
STATE  lcd_BlinkState          (void);
void   lcd_Blink					 (STATE State);
void   lcd_Cursor					 (STATE State);
STATE  lcd_BacklightState      (void);
void   lcd_BacklightChange     (STATE newState);

void   lcd_Char                (char ch, UI_8 moveCursor);
void   lcd_Txt                 (char *strPtr, UI_8 moveCursor);
void   lcd_SetCursorPos        (UI_8 x, UI_8 y);
void   lcd_GetCursorPos        (UI_8 *x, UI_8 *y);
void lcd_display_itoa(char *rxdata, UI_8 val, UI_8 x, UI_8 y, UI_8 d_x);
void lcd_display_itoa16(char *rxdata, UI_16 val, UI_8 x, UI_8 y, UI_8 d_x);
void lcd_display_string (char *rxdata, UI_8 x, UI_8 y) ;
void lcd_display_char(char *rxdata, char val, UI_8 x, UI_8 y, UI_8 d_x);
void Debug_Gothere(UI_8 nr);
void Port_WR_Parallel (int8_t data, UI_8 RS) ;
void Port_WR_Parallel_Char (char *dta, UI_8 RS);
void LCD_Custom_Char_WR (unsigned char loc, unsigned char *msg);
void lcd_Dsply_Custom(char ch, UI_8 x, UI_8 y) ;

//void DISPLAY_TEST (void) ;
/*------------------------------------------------------------------------------
                                LOCAL VARIABLES
------------------------------------------------------------------------------*/
static UI_8  lcd_x;                    /*LCD x position*/
static UI_8  lcd_y;                    /*LCD y position*/
static STATE lcd_Blink_State     = OFF;/*Assume the Blink is off at startup*/
static STATE lcd_Backlight_State = OFF;/*Assume the Backlight is off at startup*/

extern void Debug_LED_Toggle (void);

/*------------------------------------------------------------------------------
 Function   : lcd_Init
 Purpose    : Initialize the LCD Display. Set all the variables to default values
              as in config file
 Parameters : None
 Returns    : Nothing
 Notes      : Step Instruction 4 bit MODE: (NOTE - two writes each time)

 1. Power supply ON (the HD44780U is initialized by the internal reset circuit) - No display.

 2. Function set
 RS R/W DB7 DB6 DB5 DB4
 0	0	0	0	1	0	= Sets to 4-bit operation.

 3. Function set
 RS R/W DB7 DB6 DB5 DB4
 0	0	0	0	1	0  =  Sets 4-bit operation & selects 1-line display and 5 x 8 dot character font.
 0	0	0	0	*	*  =  4-bit operation starts from this step & resetting is necessary. (Number of display lines and
							character fonts cannot be changed after step #3.)
Function set:
RS		DB7 DB6 DB5 DB4		DB3 DB2 DB1 DB0
0		0	0	1	DL		N	RE	DH	RE
0		0	0	1	0		1	0	0	1	4-bit, 2 line, 5x8 dot
DL = 1 Data 8-bit lengths (DB7 to DB0) when
DL = 0 Data 4-bit lengths (DB7 to DB4) NOTE! data must be sent or received twice.
N = 1: 2 lines,		N = 0: 1 line.
F = 1: 5x10 dots,	F = 0: 5x8 dots (character font).
RE= 0/1 : extension register 
scroll/shift (DH=0: dot scroll; DH=1: display shift)
reverse bit (REV=0:normal; REV=1:inverse display)

Extended Function Set
RE RS	DB7 DB6 DB5 DB4		DB3 DB2 DB1 DB0
1	0	0	0	 0	0		1	FW	BW	NW
FW=0: 5-dot font width; FW=1: 6-dot font width
BW=0: normal cursor; BW=1: inverting cursor
NW=0: 1- or 2-line (see N); NW=1: 4-line display


 4. Display on/off control:
 RS DB7 DB6 DB5 DB4
 0		0	0	0	0	= Turns on display and cursor. Entire display is in space mode because of initialization.
 0		1	D	C	B
 0		1	1	1	1 = Display ON, Cursor ON, Blink ON
 D: 0 - Display off
 C: 0 -	Cursor off
 B: 0 - Blinking off

 5. Cursor mode set:
 RS		DB7 DB6 DB5 DB4
 0		0	0	0	1	
		DB3 DB2 DB1 DB0
 0		S/C	R/L	0	0
 0		0	1	0	0     
 S/C R/L
 0	 0 Shifts cursor position left. (AC is decremented by one.)
 0	 1 Shifts cursor position right. (AC is incremented by one.)
 1	 0 Shifts entire display  left. The cursor follows the display shift.
 1	 1 Shifts entire display  right. The cursor follows the display shift.

 6. Entry mode set:
 RS	DB7 DB6 DB5 DB4
 0		0	0	0	0	= Sets mode to increment the address by one and to shift the cursor to the right at the time of
 0		0	1	I/D	S
 0		0	1	1	0     write to the DD/CGRAM. Display is not shifted.
 I/D: 1 - Increment by 1. 0 - Decrement by 1.
 S:	  0 - No shift

 6. Write data to CGRAM/DDRAM:
 RS R/W DB7 DB6 DB5 DB4
 1	0	0	1	0	0 - Writes 'H_'. The cursor is incremented by one and shifts to the right.
 1	0	1	0	0	0
 
------------------------------------------------------------------------------*/
void lcd_Init(void)
{
	UI_8 dly = 1;	// was "1"
	//UI_8 bit = 0 ;
	//UI_8 portout = 0 ;

	delay_ms_1(100);

	/* ----------------------------------------------------
		If DDRxn is written to '0', the Pin is configured as 
	   an input.
	------------------------------------------------------*/
	DDRA |= 0x0F ;				// 0000 1111 - set LCD data port WR Direction. TMS 2.1
	DDRC |= 0xE0 ;				// 1110 0000 - set LCD port WR Direction. TMS 2.1
	DDRB |= 0x01 ;				// set LCD port EN Direction.

//while (1)
//{
	//PinWR(LED_D1, &PORTD, bit) ;
	//EN_PORT &= 0xFE ;
	//portout |= LCD_RS  ;		/* RS = 1, R/W = 0 - Command */
	//portout &= ~LCD_WR ;
	//PORTC = portout ;
	//PORTA = 0x05 ;
	//delay_ms_1(100);
//
	//PinWR(LED_D1, &PORTD, bit) ;
	//EN_PORT |= 0x01 ;
	//portout &= ~LCD_RS  ;		/* RS = 1, R/W = 0 - Command */
	//portout |= LCD_WR ;
	//PORTC = portout ;
	//PORTA = 0x0A ;
	//delay_ms_1(100);
	//bit = ~bit ;
//}

	//EN_PORT &= ~LCD_EN ;		/* Set LCD Enable = "0"  */
	// eg. EN_PORT = (1<<PB7)|(1<<PB6)|(1<<PB1)|(1<<PB0);
	EN_PORT &= 0xFE ;		/* Set LCD Enable = "0"  */
	delay_ms_1(dly);
	/*----8-bit--three times-for configuring for 8bit--------------*/
	//Port_WR_Parallel (bit_8, RS_Cmd) ;
	DATA_PORT = bit_8 ;			/* 1. Function set to 4-bit msb x1 */
	for (UI_8 i = 0; i<3; i++)
	{	
		delay_ms_1(dly)	;
		EN_PORT |= 0x01 ;			/* Set LCD Enable = "1"  */
		delay_ms_1(dly);
		EN_PORT &= 0xFE ;			/* Set LCD Enable = "0"  */
	}
	/*-------Configure-for-4-bit-bus-operation----------------------*/	
	DATA_PORT = bit_4 ;			/* 3a. Function set to 4-bit msb x4*/
	delay_ms_1(1)	;
	EN_PORT |= 0x01 ;			/* Set LCD Enable = "1"  */
	delay_ms_1(dly);
	EN_PORT &= 0xFE ;			/* Set LCD Enable = "0"  */
	delay_ms_1(dly);

	/*--------Function set-bit------------------------------------*/
	DATA_PORT = bit_4 ;			/* 3a. Set Display Font msb */
	delay_ms_1(1)	;
	EN_PORT |= 0x01 ;			/* Set LCD Enable = "1"  */
	delay_ms_1(dly);
	EN_PORT &= 0xFE ;			/* Set LCD Enable = "0"  */
	delay_ms_1(dly);

	DATA_PORT = 0x08 ;		/* 3b. Function set to 4-bit lsb x2*/
	delay_ms_1(1)	;
	EN_PORT |= 0x01 ;			/* Set LCD Enable = "1"  */
	delay_ms_1(dly);
	EN_PORT &= 0xFE ;			/* Set LCD Enable = "0"  */
	delay_ms_1(dly);
	/*------------Display-On\off-Cursor on\off-Blink on\off---*/
	DATA_PORT = 0x00 ;			/* 4a. msb */
	delay_ms_1(1)	;
	EN_PORT |= 0x01 ;			/* Set LCD Enable = "1"  */
	delay_ms_1(dly);
	EN_PORT &= 0xFE ;			/* Set LCD Enable = "0"  */
	delay_ms_1(dly);

	DATA_PORT = 0x0C ;		/* 4b. lsb Display=ON; Cursor & Blink = OFF */
	//DATA_PORT = 0x08 ;
	delay_ms_1(1)	;
	EN_PORT |= 0x01 ;			/* Set LCD Enable = "1"  */
	delay_ms_1(dly);
	EN_PORT &= 0xFE ;			/* Set LCD Enable = "0"  */
	delay_ms_1(dly);
	/*------------Entry mode-set------------------------------*/
	DATA_PORT = 0x00 ;			/* 5a. msb */
	delay_ms_1(1)	;
	EN_PORT |= 0x01 ;			/* Set LCD Enable = "1"  */
	delay_ms_1(dly);
	EN_PORT &= 0xFE ;			/* Set LCD Enable = "0"  */
	delay_ms_1(dly);

	DATA_PORT = 0x06 ;			/* 5b. lsb */
	delay_ms_1(1)	;
	EN_PORT |= 0x01 ;			/* Set LCD Enable = "1"  */
	delay_ms_1(dly);
	EN_PORT &= 0xFE ;			/* Set LCD Enable = "0"  */
	delay_ms_1(dly);

	//DISPLAY_TEST();
}

/*------------------------------------------------------------------------------
 Function   : Port_WR_Parallel (integer)
 Purpose    : Writes byte out on LCD port via PARALELL data BUS in two 
				: 4bit transactions.
 Parameters : 8 bit 'info' byte.
				: Register Select RS = '1' for data or '0' for Command.
				: bits = nr of bits.
 Returns    : Nothing
 Notes      :
------------------------------------------------------------------------------*/
void Port_WR_Parallel (int8_t data, UI_8 RS)
{
	UI_8 portout = 0, msb_data = 0, lsb_data = 0 ;
	UI_8 dly = 1;	// was "1"

	DDRA |= 0x0F ;			/* Set data bit direction  */
	PINB &= 0xFE ;			/* Clear EN  */
	delay_ms_1(dly);
	
	msb_data = data & 0xF0 ;	/* Mask out Msb's */
	msb_data = msb_data >> 4 ; /* shift msb to Lsb position */
	lsb_data = data & 0x0F ;	/* Mask out lsb's */	
	
	PINB &= 0xFE ;	
	delay_ms_1(dly);	

	/* Set for Data reg RS = '1' for data transfer or '0' for Command
		Configure for Write operation.
	-----------------------------------------------------------------*/
	if (RS)	/* Data WR */
	{	
		portout |= LCD_RS  ;		/* RS = 1, R/W = 0 - Command */
		portout &= ~LCD_WR ; 
		CTRL_PORT = portout ;
	}  /* RS = 1 */
	else		/* Command(instruction) WR */
	{		
		/* RS, R/W = 0 - Command */	
		CTRL_PORT &= 0x9F ;			/* TMS 2.0*/
	
	} 	/* RS, R/W = 0 */	

	/* MSB Write instruction to data bus 
	-------------------------------------*/	
	portout &= 0xF0  ;		/* Clear lsb */
	portout |= msb_data  ;
	DATA_PORT = portout ;
	delay_ms_1(1)	;

	/*				/������������������
	EN  ______ /						*/		 
	EN_PORT |= LCD_EN ;	/* Set LCD Enable  */
	delay_ms_1(dly);
					
			
	/*	 ������������������\
	EN		   				  \________*/
	
	EN_PORT &= 0xFE ;			/* Clear LCD Enable to latch data */
	delay_ms_1(dly);

	/* LSB Write instruction to databus 
	------------------------------------*/		
	portout &= 0xF0  ;		/* Clear lsb */
	portout |= lsb_data  ;
	DATA_PORT = portout ;
	delay_ms_1(1)	;

	/*				/������������������
	EN  ______ /						*/		 
	EN_PORT |= LCD_EN ;	/* Set LCD Enable "data + 110x" */
	delay_ms_1(dly);
		
			
	/*	 ������������������\
	EN  				        \________*/
	EN_PORT &= 0xFE ;			/* Clear LCD Enable to latch data */
	delay_ms_1(dly);

	return ;
}

/*------------------------------------------------------------------------------
 Function   : lcd_display_string
 Purpose    : Sends received string to LCD Display at location.
              as in config file
 Parameters : String, row, colum (x,y)
 Returns    : Nothing
 Notes      : 
------------------------------------------------------------------------------*/
void lcd_display_string (char *strPtr, UI_8 x, UI_8 y)
{
	unsigned char cSREG;

	cSREG = SREG;	
	asm("cli");			

	//lcd_Cmd(LCD_DISP_ON);	
	lcd_SetCursorPos(x, y);

	/* While not end of text column*/
	/* While not reached end of text (/0)*/
	while ((x++ < LCD_TOTAL_COL) && (*strPtr != '\0'))
	{
		Port_WR_Parallel (*strPtr, RS_Data) ;
		++strPtr;
	}
	lcd_x = x ; //start at current x position
	lcd_y = y ; //start at current y position
	lcd_SetCursorPos(x, y);

	SREG = cSREG;
}

/*------------------------------------------------------------------------------
 Function   : lcd_display_itoa_signed
 Purpose    : Takes a string, converts it to an integer and sends it 
					to LCD Display at location.
              as in config file
 Parameters : String, row, colum (x,y)
 Returns    : Nothing
 Notes      : This function can display negative numbers

 auther: Wayne Botha
------------------------------------------------------------------------------*/
void lcd_display_itoa_signed(char *rxdata, int8_t val, UI_8 x, UI_8 y, UI_8 d_x)
{
	char buffer[10];
	unsigned char cSREG;

	cSREG = SREG;
	asm("cli");

	lcd_SetCursorPos(x, y);
	lcd_Txt(rxdata, true);
	/*converts int8_t data type to string data type*/
	itoa(val, buffer, 10);		//Populate 'buffer' array with 'val'
	//strcpy(buffer,"-1");
	lcd_SetCursorPos(d_x, y);
	lcd_Txt(buffer, true);
	SREG = cSREG;
}
/*------------------------------------------------------------------------------
 Function   : lcd_display_itoa
 Purpose    : Takes a integer and converts it to a string and sends it 
				  to LCD Display at location.
              as in config file
 Parameters : String, row, colum (x,y)
 Returns    : Nothing
 Notes      : 
------------------------------------------------------------------------------*/
void lcd_display_itoa(char *rxdata, UI_8 val, UI_8 x, UI_8 y, UI_8 d_x)
{
	char buffer[10];
	unsigned char cSREG;

	cSREG = SREG;
	asm("cli");

	lcd_SetCursorPos(x, y);
	while ((x++ < LCD_TOTAL_COL) && (*rxdata != '\0'))
	{
		Port_WR_Parallel (*rxdata, RS_Data) ;
		++rxdata;
	}
	lcd_x = x ; //start at current x position
	lcd_y = y ; //start at current y position
	
	/*converts uint8_t data type to string data type
	------------------------------------------------*/
	itoa(val, buffer, 10);		//Populate 'buffer' array with 'val'
	lcd_SetCursorPos(d_x, y);
	rxdata = buffer ;		/* same as *rxdata = &buffer[0]*/
	
	while ((x++ < LCD_TOTAL_COL) && (*rxdata != '\0'))
	{
		Port_WR_Parallel (*rxdata, RS_Data) ;
		++rxdata;
	}
	lcd_x = x ; //start at current x position
	lcd_y = y ; //start at current y position
	lcd_SetCursorPos(x, y);
	SREG = cSREG;
}
/**************************************************************************/
void lcd_display_itoa16(char *rxdata, UI_16 val, UI_8 x, UI_8 y, UI_8 d_x)
{
	char buffer[10];
	unsigned char cSREG;

	cSREG = SREG;
	asm("cli");

	lcd_SetCursorPos(x, y);

	while ((x++ < LCD_TOTAL_COL) && (*rxdata != '\0'))
	{
		Port_WR_Parallel (*rxdata, RS_Data) ;
		++rxdata;
	}
	lcd_x = x ; //start at current x position
	lcd_y = y ; //start at current y position

	/*converts uint8_t data type to string data type*/
	itoa(val, buffer, 10);		//Populate 'buffer' array with 'val'
	lcd_SetCursorPos(d_x, y);
	rxdata = buffer ;		/* same as *rxdata = &buffer[0] */
	while ((x++ < LCD_TOTAL_COL) && (*rxdata != '\0'))
	{
		Port_WR_Parallel (*rxdata, RS_Data) ;
		++rxdata;
	}
	lcd_x = x ; //start at current x position
	lcd_y = y ; //start at current y position
	lcd_SetCursorPos(x, y);
	SREG = cSREG;
}
/*------------------------------------------------------------------------------
 Function   : lcd_dsply_itoa_8bit_sign
 Purpose    : Takes a string, converts it to an integer and sends it 
					to LCD Display at location.
              as in config file
 Parameters : String, row, column (x,y)
 Returns    : Nothing
 Notes      : This function can display negative numbers -1 to -127
					the msbit is the sin bit.					
------------------------------------------------------------------------------*/
void lcd_dsply_itoa_8bit_sign(char *rxdata, int8_t val, UI_8 x, UI_8 y, UI_8 d_x)
{
	UI_8 i ;
	char strng[6] ;
	unsigned char cSREG;

	cSREG = SREG;
	asm("cli");

	console_writeline("\n\rval: %d", val) ;

	lcd_SetCursorPos(x, y);
	while ((x++ < LCD_TOTAL_COL) && (*rxdata != '\0'))
	{
		Port_WR_Parallel (*rxdata, RS_Data) ;
		++rxdata;
	}

	if (val > 127)
	{
		val = ~val ;
		val++ ;		
		sprintf(strng, "%s%d", "-", val) ;
	}
	else
	{
		sprintf(strng, "%d", val ) ;
	}
	console_writeline("\n\r2's: %d", val) ;
	
	lcd_SetCursorPos(d_x, y);
	rxdata = strng ;
	//while ((x++ < LCD_TOTAL_COL) && (*rxdata != '\0'))
	i = 0 ;
	while (*rxdata != '\0')
	{
		Port_WR_Parallel (*rxdata, RS_Data) ;
		++rxdata;
		console_writeline("\n\rx: %d", i) ;
		i++ ;
	}
	lcd_SetCursorPos(x, y);
	SREG = cSREG;
}

/*------------------------------------------------------------------------------
 Function   : lcd_dsply_int8_sign
 Purpose    : Takes a int8_t converts it to an string and sends it 
					to LCD Display at x, y position.
 Parameters : String, row, column (x,y)
 Returns    : Nothing
 Notes      : This function can display negative numbers -1 to -127
					the msbit is the sin bit.					

2021-04-30: This code was changed in the debug process of the negative number display
problem and has not been verified again, so caution in using this routine.
Include "lcd_SetCursorPos(x, y);" at the end because this caused a problem in the 
Lcd_Display_String() function.
------------------------------------------------------------------------------*/
void lcd_dsply_int8_sign(int8_t val, UI_8 x, UI_8 y)
{
	UI_8 i = 0 ;
	char strng[5] = "" ;
	char *strptr ;
	UI_8 debug = 0 ;

	unsigned char cSREG;

	//strng[4] = '\0' ;
	strptr = strng ;
	/* ------------------------------------
		Verification test of received value 
	---------------------------------------*/
	if (debug)
	{
		console_writeline("\n\rval: %d", val) ;
	}
	cSREG = SREG;
	asm("cli");
//	if (val > 127 && val <= 255)			
	if (val > 127)
	{
		val = ~val ;
		val++ ;
		//sprintf(strng, "%s%d", "-", val) ;
		//sprintf(strng, "%d%c", val, '\0') ;
		sprintf(strng, "%d", val) ;
		if (debug)
		{
			console_writeline("\n\rneg val:, %d", val) ;
		}
	}
	else
	{
		//sprintf(strng, "%d%c", val, '\0') ;
		sprintf(strng, "%d", val) ;
		//sprintf(strng, "%s%d", "+", val) ;
		if (debug)
		{
			console_writeline("\n\rpos val: %d", val) ;
		}
	}
	if (debug)
	{
		console_writeline("\n\rlcd_dsply_itoa_8bit: ") ;
		console_writeline(strng) ;
	}
	
	lcd_SetCursorPos(x, y);

	//while ((x++ < 5) && (*strptr != '\0'))
	console_writeline("\n\r") ;
	//i = 0 ;
	//while (*strptr != '\0')
	while ((x++ < LCD_TOTAL_COL) && (strng[i] != '\0'))
	{
		Port_WR_Parallel (strng[i], RS_Data) ;
		//Port_WR_Parallel (strptr, RS_Data) ;
		++strptr ;
		console_write("%c", strng[i]) ;		
		i++ ;
	}
	lcd_SetCursorPos(x, y);
	SREG = cSREG;
}
/*------------------------------------------------------------------------------
 Function   : lcd_display_char
 Purpose    : Sends received string to LCD Display at location.
              as in config file
 Parameters : String, row, column (x,y)
 Returns    : Nothing
 Notes      : 
------------------------------------------------------------------------------*/
void lcd_display_char (char *rxdata, char val, UI_8 x, UI_8 y, UI_8 d_x)
{
	unsigned char cSREG;

	cSREG = SREG;	
	asm("cli");

	lcd_SetCursorPos(x, y);
	while ((x++ < LCD_TOTAL_COL) && (*rxdata != '\0'))
	{
		Port_WR_Parallel (*rxdata, RS_Data) ;
		++rxdata;
	}
	lcd_SetCursorPos(d_x, y);
	lcd_Char(val, true);
	SREG = cSREG;
}
/*------------------------------------------------------------------------------
 Function   : lcd_display_Schar
 Purpose    : Sends a single char to LCD Display at location.
              as in config file
 Parameters : String, row, column (x,y)
 Returns    : Nothing
 Notes      : 
------------------------------------------------------------------------------*/
void lcd_display_Schar (char val, UI_8 x, UI_8 y)
{
	unsigned char cSREG;

	cSREG = SREG;	
	asm("cli");

	lcd_SetCursorPos(x, y);
	lcd_Char(val, true);
	SREG = cSREG;
}
/*------------------------------------------------------------------------------
 Function   : lcd_display_hex - NOT COMPLETE!!!!!!!!!
 Purpose    : Sends received string to LCD Display at location.
              as in config file
 Parameters : String, row, colum (x,y)
 Returns    : Nothing
 Notes      : 
------------------------------------------------------------------------------*/
#if 0

void lcd_display_hex (char *rxdata, char val, UI_8 x, UI_8 y, UI_8 d_x)
{
	unsigned char cSREG;
	cSREG = SREG;
	 
	asm("cli");
	lcd_SetCursorPos(x, y);
	lcd_Txt(rxdata, true);
	lcd_SetCursorPos(d_x, y);
	lcd_Char(val, true);
	SREG = cSREG;
}
#endif

/*------------------------------------------------------------------------------
 Function   : lcd_Clear
 Purpose    : Clear the display of any characters
 Parameters : None
 Returns    : Nothing
 Notes      :
------------------------------------------------------------------------------*/
void lcd_Clear (void)
{
   lcd_Cmd(LCD_CLR);
   lcd_SetCursorPos(0,0);
}
/*------------------------------------------------------------------------------
 Function   : lcd_display
 Purpose    : Set the cursor ON = 1, OFF = 0
 Parameters : None
 Returns    : Nothing
 Notes      :
------------------------------------------------------------------------------*/
void lcd_display (STATE State)
{
	if (State == 1)
	{
		lcd_Cmd(LCD_DISP_ON);       
	}
	else
	{
		lcd_Cmd(LCD_DISP_OFF);  
	}
}
/*------------------------------------------------------------------------------
 Function   : lcd_Home
 Purpose    : Set the display to Home position
 Parameters : None
 Returns    : Nothing
 Notes      :
------------------------------------------------------------------------------*/
void lcd_Home (void)
{
	lcd_Cmd(LCD_HOME);     //Difference between LCD_HOME and setting cursor (0,0)??
    lcd_SetCursorPos(0,0); 
}
/*------------------------------------------------------------------------------
 Function   : lcd_Cursor
 Purpose    : Set the cursor ON = 1, OFF = 0
 Parameters : None
 Returns    : Nothing
 Notes      :
------------------------------------------------------------------------------*/
void lcd_Cursor (STATE State)
{
	if (State == 1)
	{
		lcd_Cmd(LCD_CURS_ON);        
	}
	else
	{
		lcd_Cmd(LCD_CURS_OFF);  
	}
}
/*------------------------------------------------------------------------------
Function   : Scroll Enable: 
RE RS	DB7 DB6 DB5 DB4		DB3 DB2 DB1 DB0
bit
1 0		0	0	0	1		H4	H3	H2	H1	
		0	0	0	1		x	x	x	x
 Purpose    : Determine the line for horizontal scroll
 Parameters : Line
 Returns    : Nothing
 Notes      : Function - Not tested
------------------------------------------------------------------------------*/
void lcd_Scroll_Enable (UI_8 line)
{
  UI_8 portout = 0x10;

	portout &= line ;
	lcd_Cmd(portout);     //Difference between LCD_HOME and setting cursor (0,0)??
   
   lcd_SetCursorPos(0,0); //?????????????????

   return;
}

/*------------------------------------------------------------------------------
 Function   : lcd_BlinkState
 Purpose    : Returns the state of cursor blinking
 Parameters : Nothing
 Returns    : ON if active, otherwise OFF
 Notes      :
------------------------------------------------------------------------------*/
STATE lcd_BlinkState (void)
{
   return lcd_Blink_State;
}

/*------------------------------------------------------------------------------
 Function   : lcd_Blink
 Purpose    : Set the cursor blinking to on or off
 Parameters : newState (0 = off, 1 = on)
 Returns    : Nothing
 Notes      :
------------------------------------------------------------------------------*/
void lcd_Blink (STATE State)
{
   if (State == OFF)
   {
      lcd_Cmd(LCD_BLINK_OFF);
   }
   else
   {
      lcd_Cmd(LCD_BLINK_ON);
   }
   lcd_Blink_State = State;
}

/*------------------------------------------------------------------------------
 Function   : lcd_BacklightState
 Purpose    : Returns the state of cursor blinking
 Parameters : Nothing
 Returns    : ON if active, otherwise OFF
 Notes      :
------------------------------------------------------------------------------*/
STATE lcd_BacklightState (void)
{
   return lcd_Backlight_State;
}

/*------------------------------------------------------------------------------
 Function   : lcd_BacklightChange
 Purpose    : Set the backlight to on or off
 Parameters : newState (0 = off, 1 = on)
 Returns    : Nothing
 Notes      :
------------------------------------------------------------------------------*/
void lcd_BacklightChange (STATE newState)
{
   if (newState == OFF)
   {
	   BL_PORT &= ~(1 << BL_PIN);
   }
   else
   {
	   BL_PORT |= (1 << BL_PIN);
   }
   lcd_Backlight_State = newState;
}

/*------------------------------------------------------------------------------
 Function   : lcd_Char
 Purpose    : Writes text to the LCD at the current (x,y) position
 Parameters : ch - single character to display
            : moveCursor - move the cursor to end of text if value is TRUE
 Returns    : Nothing
 Notes      :
------------------------------------------------------------------------------*/
void lcd_Char(char ch, UI_8 moveCursor)
{
	unsigned char cSREG;
	cSREG = SREG;
	asm("cli") ;
		
	UI_8 x = lcd_x; //start at current x position
	UI_8 y = lcd_y; //start at current y position	

	lcd_SetCursorPos(x, y);
	if (x < LCD_TOTAL_COL) /* While not end of text column*/
	{
		lcd_Data(ch);
		++x;
	}
	if (moveCursor == true)
	{
		lcd_SetCursorPos(x, y);
	} /* if moveCursor is true */
	else
	{
		lcd_SetCursorPos(lcd_x, lcd_y);
	}

	SREG = cSREG;
}

/*------------------------------------------------------------------------------
 Function   : lcd_Txt
 Purpose    : Writes text string to the LCD at the current (x,y) position
 Parameters : strPtr - String pointer
            : moveCursor - move the cursor to end of text if value is TRUE
 Returns    : Nothing
 Notes      :
------------------------------------------------------------------------------*/
void lcd_Txt (char *strPtr, UI_8 moveCursor)
{
    unsigned char cSREG ;
    cSREG = SREG ;
    asm("cli") ;

	UI_8 x = lcd_x ; //start at current x position
	UI_8 y = lcd_y ; //start at current y position
	
	lcd_SetCursorPos(x, y);

		/* While not end of text column*/
		/* While not reached end of text (/0)*/
	while ((x++ < LCD_TOTAL_COL) && (*strPtr != '\0'))		
	{
			lcd_Data(*strPtr);
		++strPtr;
	}
	if (moveCursor == true)
	{
		lcd_SetCursorPos(x, y);
	} /* if moveCursor is true */
	else
	{
		lcd_SetCursorPos(lcd_x, lcd_y);
	}

	SREG = cSREG;
}

/********************************************************************
 *       Function Name:  HextoASCII                                  *
 *       Return Value:	 char ptr to Display                             *
 *       Parameters:     unsigned hex number                         *
 *       Description:    This routine will display a hex number on   *
 *                       LCD by converting the number to ASCII       *
********************************************************************/
unsigned char HextoASCII(unsigned char *hex)
{
    unsigned char temp_L;           // dummy variable that will hold LSBs of Hex number
    unsigned char temp_H;           // dummy variable that will hold MSBs of Hex number
	unsigned char c[2] ;
  
    temp_H = *hex & 0xF0;           // Obtain the upper 4 bits (MSBs) of hex number
    temp_H = temp_H >> 4;           // 
    if(temp_H >9)                   // Check if the number is a letter
         temp_H += 0x37;             // Convert the number to a letter in ASCII
     else
		c[1] = (temp_H);				// char to Display
     temp_L = *hex & 0x0F;           // Obtain the lower 4 bits (LSBs) of hex number
     if(temp_L >9)                   // Check if the the number is a letter
         temp_L += 0x37;             // Convert the number to a letter in ASCII
     else
         temp_L += 0x30;             // Convert the number to ASCII number
     c[0] = (char)(temp_L);				// char to Display
	 return (c[0]) ;
 }   

/*------------------------------------------------------------------------------
 Function   : lcd_SetCursorPos
 Purpose    : Set the cursor to the coordinates given by inputs x and y
 Parameters : x - column number, y - row number
 Returns    : Nothing
 Notes      :
 2021-04-30: The over run to the next line is incorrect and was removed for x & y
------------------------------------------------------------------------------*/
#if 1
void lcd_SetCursorPos (UI_8 x, UI_8 y)
{
   UI_8 cursor;   /* DDRAM address value for cursor */
	
   switch (y)    /* Set the row address */
   {
		case 0x00:
			cursor = LCD_ROW1;
			break;

		case 0x01:
			cursor = LCD_ROW2;
			break;

		case 0x02:
			cursor = LCD_ROW3;
			break;

		case 0x03:
			cursor = LCD_ROW4;
			break;

		default:
			cursor = LCD_ROW1;
   }

   cursor += x;            /* Add the column address to row address */
   cursor |= 0x80;         /* Set bit 7 to change DDRAM*/
   lcd_Cmd(cursor);        /* Update DDRAM address to change cursor */  

	lcd_x = x;
	lcd_y = y;

   return;
}
#endif


/*------------------------------------------------------------------------------
 Function   : lcd_GetCursorPos
 Purpose    : Get the current cursor coordinates
 Parameters : x - column number, y - row number
 Returns    : Nothing
 Notes      :
------------------------------------------------------------------------------*/
void lcd_GetCursorPos (UI_8 *x, UI_8 *y)
{
   *x = lcd_x;
   *y = lcd_y;
   return;
}

/*------------------------------------------------------------------------------
 Function   : LCD_Custom_Char
 Purpose    : Stores a Pattern in CGRAM
 Parameters : data - the data byte to write to LCD
 Returns    : Nothing
 Notes      : Loc is position in DDRAM
------------------------------------------------------------------------------*/
void LCD_Custom_Char_WR (unsigned char loc, unsigned char *data)
{
    unsigned char i;
    
	 if(loc < 8)
    {
		lcd_Cmd(CGRAMWR + (loc << 3));  /* Command 0x40 and onwards forces */
                                  /* the device to point CGRAM address */
      for(i=0;i<8;i++)  /* Write char rows to CGRAM */
          // lcd_Char(msg[i], true);      
			  lcd_Data(data[i]);
   }   
}
/*------------------------------------------------------------------------------
 Function   : lcd_Custom_Char_Dsply
 Purpose    : Prints a custom character on LCD 16x2.				
 Parameters : None
 Returns    : Nothing
 Notes      : To display custom characters, simply provide custom character
				  number (from 0 to 7) as a data to LCD16x2.
------------------------------------------------------------------------------*/
void lcd_Custom_Char_Dsply()
{
	//char i;
	
	//unsigned char HeartBlank[8] = { 0x00, 0x0A, 0x15, 0x11, 0x0A, 0x04, 0x00, 0x00 };  /* Custom char set for alphanumeric LCD Module */
	//unsigned char Phone[8] = { 0x04, 0x1F, 0x11, 0x11, 0x1F, 0x1F, 0x1F, 0x1F };
	//unsigned char Bell[8] = { 0x04, 0x0E, 0x0E, 0x0E, 0x1F, 0x00, 0x04, 0x00 };
	//unsigned char Speaker[8] = { 0x01, 0x03, 0x07, 0x1F, 0x1F, 0x07, 0x03, 0x01 };
	//unsigned char Note[8] = { 0x01, 0x03, 0x05, 0x09, 0x09, 0x0B, 0x1B, 0x18 };
	//unsigned char Power[8] = { 0x0A, 0x0A, 0x1F, 0x11, 0x11, 0x0E, 0x04, 0x04 };
	//unsigned char smiley[8] = { 0x00, 0x00, 0x0A, 0x00, 0x04, 0x11, 0x0E, 0x00 };
	//unsigned char Heartfull[8] = { 0x00, 0x0A, 0x1F, 0x1F, 0x0E, 0x04, 0x00, 0x00 };
	unsigned char Celcius[8] = { 0x18, 0x18, 0x0E, 0x11, 0x10, 0x11, 0x0E, 0x00 };
		
	//unsigned char Sig_1[8] = { 0x00, 0x00, 0x00, 0x00, 0x10, 0x10, 0x10, 0x00 };
	//unsigned char Sig_2[8] = { 0x00, 0x00, 0x00, 0x01, 0x03, 0x07, 0x0F, 0x00 };
	//unsigned char Sig_3[8] = { 0x00, 0x00, 0x01, 0x03, 0x07, 0x0F, 0x1F, 0x00 };
	//unsigned char Sig_4[8] = { 0x00, 0x01, 0x03, 0x07, 0x0F, 0x1F, 0x1F, 0x00 };
	//unsigned char Sig_5[8] = { 0x01, 0x03, 0x07, 0x0F, 0x1F, 0x1F, 0x1F, 0x00 };
		
	unsigned char Sig_1[8] = { 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0x00 };
	unsigned char Sig_2[8] = { 0x00, 0x00, 0x00, 0x00, 0x00, 0x1F, 0x1F, 0x00 };
	unsigned char Sig_3[8] = { 0x00, 0x00, 0x00, 0x00, 0x1F, 0x1F, 0x1F, 0x00 };
	unsigned char Sig_4[8] = { 0x00, 0x00, 0x00,0x1F, 0x1F, 0x1F, 0x1F, 0x00 };
	unsigned char Sig_5[8] = { 0x00, 0x00, 0x1F, 0x1F, 0x1F, 0x1F, 0x1F, 0x00 };
	unsigned char Sig_6[8] = { 0x00, 0x1F, 0x1F,0x1F, 0x1F, 0x1F, 0x1F, 0x00 };
	unsigned char Sig_7[8] = { 0x1F, 0x1F, 0x1F, 0x1F, 0x1F, 0x1F, 0x1F, 0x00 };	
		
	//unsigned char SignalF[8] = { 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00 };
	//unsigned char SignalG[8] = { 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00 };
	//unsigned char SignalH[8] = { 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00 };	
		
	//unsigned char Celcius[8] = { 0x18, 0x18, 0x0E, 0x11, 0x10, 0x10, 0x11, 0x0E };
	//unsigned char Celcius[8] = { 0x08, 0x14, 0x08, 0x06, 0x09, 0x08, 0x09, 0x06 };
	//unsigned char Celcius[8] = { 0x1C, 0x14, 0x1C, 0x06, 0x09, 0x08, 0x09, 0x06 };

	//LCD_Custom_Char_WR(HEARTBLNK_C, HeartBlank);	/* Build Character1 at position 0 */
	//LCD_Custom_Char_WR(PHONE_C, Phone);				/* Build Character2 at position 1 */
	//LCD_Custom_Char_WR(BELL_C, Bell);				/* Build Character3 at position 2 */
	//LCD_Custom_Char_WR(SPEAKR_C, Speaker);			/* Build Character4 at position 3 */
	LCD_Custom_Char_WR(CELCIUS_C, Celcius);			/*Write pattern to CGRAM data addr 0x00 */
	//LCD_Custom_Char_WR(4, Note);					/* Build Character5 at position 4 */
	//LCD_Custom_Char_WR(POWER_C, Power);				/* Build Character6 at position 5 */
	//LCD_Custom_Char_WR(SMILE_C, smiley);			/* Build Character7 at position 6 */
	//LCD_Custom_Char_WR(HEARTFUL_C, Heartfull);		/* Build Character8 at position 7 */
	LCD_Custom_Char_WR(SIG_1, Sig_1);				/* Build Character9 at position 8 */
	LCD_Custom_Char_WR(SIG_2, Sig_2);				/* Build Character10 at position 9 */
	LCD_Custom_Char_WR(SIG_3, Sig_3);				/* Build Character11 at position 10 */
	LCD_Custom_Char_WR(SIG_4, Sig_4);				/* Build Character12 at position 11 */
	LCD_Custom_Char_WR(SIG_5, Sig_5);				/* Build Character13 at position 12 */
	LCD_Custom_Char_WR(SIG_6, Sig_6);				/* Build Character14 at position 13 */
	LCD_Custom_Char_WR(SIG_7, Sig_7);				/* Build Character15 at position 14 */

	//lcd_display_string("Custom Char:", 0, LCD_LINE_1);
//
	//delay_ms_1(2);			/* Clear display command delay> 1.63 ms */
//
	//lcd_SetCursorPos(0, 1);
	//
	//for(i=0;i<8;i++)		/* function will send data 1 to 8 to lcd */
	//{
		//lcd_Char(i, true);		
		//lcd_Char(' ', true);		/* space between each custom char. */
	//}	
}
/*------------------------------------------------------------------------------
 Function   : void lcd_Dsply_Custom()
 Purpose    : Prints a custom character on LCD 16x2.				
 Parameters : None
 Returns    : Nothing
 Notes      : To display custom characters, simply provide custom character
				  number (from 0 to 7) as a data to LCD16x2.
------------------------------------------------------------------------------*/
void lcd_Dsply_Custom(char ch, UI_8 x, UI_8 y) 
{
	lcd_SetCursorPos(x, y) ;
	lcd_Char(ch, true) ;
}
/*------------------------------------------------------------------------------
 Function   : lcd_Cmd
 Purpose    : Write command instruction to the LCD controller.
				: 4 bit mode - write MSB then LSB
 Parameters : cmd - Byte to write to LCD
 Returns    : Nothing
 Notes      :
------------------------------------------------------------------------------*/
void lcd_Cmd (UI_8 cmd)
{
	Port_WR_Parallel (cmd, 0) ;		/* RS = CMD Register(0), 4bit */	
}

/*------------------------------------------------------------------------------
 Function   : lcd_Data
 Purpose    : Write data instruction to the LCD controller
 Parameters : data - the data byte to write to LCD
 Returns    : Nothing
 Notes      :
------------------------------------------------------------------------------*/
void lcd_Data(UI_8 data)
{
	Port_WR_Parallel (data, 1) ;		/* RS = DATA Register(1) */
}

/**************************************************************************/
void Debug_Gothere(UI_8 nr)
{
	unsigned char cSREG;
	cSREG = SREG;
	
	asm("cli");
	lcd_Clear() ;

	lcd_display_itoa ("Got here:", nr, 0, LCD_LINE_1, (10));	
	
	delay_ms_1(1000);

	SREG = cSREG;
}
/**************************************************************************/
#if 0
void DISPLAY_TEST (void)
{
	UI_8 dly = 5;	// was "1"
	char *strptr = NULL ;
	
	Port_WR_Parallel (LCD_HOME, RS_Cmd) ;
	Port_WR_Parallel (LCD_CLR, RS_Cmd) ;
	
	/*-----------Write "HITMAN"------------------------------------*/
	strptr = " Cold Room V2.0" ;
	lcd_display_string(strptr, 0, LCD_LINE_1);

	Port_WR_Parallel (0x48, RS_Data) ; /* H */
	Port_WR_Parallel (0x49, RS_Data) ; /* I */
	Port_WR_Parallel (0x54, RS_Data) ; /* T */
	Port_WR_Parallel (0x4D, RS_Data) ; /* M */
	
	/*--------------Write "A"----------------------------------*/
	DATA_PORT = 0x14 ;			/* 8a. Set RS = 1*/
	delay_ms_1(1)	;
	EN_PORT |= 0x01 ;			/* Set LCD Enable = "1"  */
	delay_ms_1(dly);
	EN_PORT &= 0xFE ;			/* Set LCD Enable = "0"  */
	delay_ms_1(dly);
	
	DATA_PORT = 0x11 ;			/* 8b. 0001 0100 Set RS = 1*/
	delay_ms_1(1)	;
	EN_PORT |= 0x01 ;			/* Set LCD Enable = "1"  */
	delay_ms_1(dly);
	EN_PORT &= 0xFE ;			/* Set LCD Enable = "0"  */
	delay_ms_1(dly);
	/*--------------Write "N"----------------------------------*/
	DATA_PORT = 0x14 ;			/* 8a. Set RS = 1*/
	delay_ms_1(1)	;
	EN_PORT |= 0x01 ;			/* Set LCD Enable = "1"  */
	delay_ms_1(dly);
	EN_PORT &= 0xFE ;			/* Set LCD Enable = "0"  */
	delay_ms_1(dly);
	
	DATA_PORT = 0x1E ;			/* 8b. 0001 0100 Set RS = 1*/
	delay_ms_1(1)	;
	EN_PORT |= 0x01 ;			/* Set LCD Enable = "1"  */
	delay_ms_1(dly);
	EN_PORT &= 0xFE ;			/* Set LCD Enable = "0"  */
	delay_ms_1(dly);

	HALT
}
#endif
//#if 0
//void LCD_TEST(void)
//{
	////UI_8 portout = 0, msb_data = 0, lsb_data = 0 ;
	//UI_8 dly = 5;	// was "1"
//
	//delay_ms_1(100);
	//
	////msb_data = data & 0xF0 ;	/* Mask out Msb's */
	////msb_data = msb_data >> 4 ; /* shift msb to Lsb position */
	////lsb_data = data & 0x0F ;	/* Mask out lsb's */
	//
	//DDRA |= 0x3F ;				// 0011 1111 - set LCD port WR Direction.
	//DDRB |= 0x01 ;				// set LCD port EN Direction.
//
	////EN_PORT &= ~LCD_EN ;		/* Set LCD Enable = "0"  */
	//// eg. EN_PORT = (1<<PB7)|(1<<PB6)|(1<<PB1)|(1<<PB0);
	//EN_PORT &= 0xFE ;
	//delay_ms_1(dly);
	///*----8-bit---------------------------------------------------*/
	////Port_WR_Parallel (bit_8, RS_Cmd) ;
	//DATA_PORT = bit_8 ;			/* 1. Function set to 4-bit msb x1 */
//
	//delay_ms_1(1)	;
	//EN_PORT |= 0x01 ;			/* Set LCD Enable = "1"  */
	//delay_ms_1(dly);
	//EN_PORT &= 0xFE ;			/* Set LCD Enable = "0"  */
	////EN_PORT &= 0x00 ;
	//delay_ms_1(dly);
	///*----8-bit---------------------------------------------------*/
	////DATA_PORT = 0x20 ;			/* 2. Function set to 4-bit lsb x2*/
	////delay_ms_1(1)	;
	//EN_PORT |= 0x01 ;			/* Set LCD Enable = "1"  */
	//delay_ms_1(dly);
	//EN_PORT &= 0xFE ;			/* Set LCD Enable = "0"  */
	//delay_ms_1(dly);
	///*----8-bit---------------------------------------------------*/
	////DATA_PORT = 0x20 ;			/* 3a. Function set to 4-bit msb x3*/
	////delay_ms_1(1)	;
	//EN_PORT |= 0x01 ;			/* Set LCD Enable = "1"  */
	//delay_ms_1(dly);
	//EN_PORT &= 0xFE ;			/* Set LCD Enable = "0"  */
	//delay_ms_1(dly);
	///*-------------------4-bit------------------------------------*/
	//DATA_PORT = bit_4 ;			/* 3a. Function set to 4-bit msb x4*/
	//delay_ms_1(1)	;
	//EN_PORT |= 0x01 ;			/* Set LCD Enable = "1"  */
	//delay_ms_1(dly);
	//EN_PORT &= 0xFE ;			/* Set LCD Enable = "0"  */
	//delay_ms_1(dly);
//
	///*--------Function set-bit------------------------------------*/
	//DATA_PORT = bit_4 ;			/* 3a. Function set to 4-bit msb x2*/
	//delay_ms_1(1)	;
	//EN_PORT |= 0x01 ;			/* Set LCD Enable = "1"  */
	//delay_ms_1(dly);
	//EN_PORT &= 0xFE ;			/* Set LCD Enable = "0"  */
	//delay_ms_1(dly);
//
	//DATA_PORT = 0x08 ;			/* 3b. Function set to 4-bit lsb x2*/
	//delay_ms_1(1)	;
	//EN_PORT |= 0x01 ;			/* Set LCD Enable = "1"  */
	//delay_ms_1(dly);
	//EN_PORT &= 0xFE ;			/* Set LCD Enable = "0"  */
	//delay_ms_1(dly);
	///*------------Display-On----------------------------------*/
	//DATA_PORT = 0x00 ;			/* 4a. */
	//delay_ms_1(1)	;
	//EN_PORT |= 0x01 ;			/* Set LCD Enable = "1"  */
	//delay_ms_1(dly);
	//EN_PORT &= 0xFE ;			/* Set LCD Enable = "0"  */
	//delay_ms_1(dly);
//
	//DATA_PORT = 0x0F ;			/* 4b. */
	//delay_ms_1(1)	;
	//EN_PORT |= 0x01 ;			/* Set LCD Enable = "1"  */
	//delay_ms_1(dly);
	//EN_PORT &= 0xFE ;			/* Set LCD Enable = "0"  */
	//delay_ms_1(dly);
	///*------------Entry mode-set------------------------------*/
	//DATA_PORT = 0x00 ;			/* 5a. */
	//delay_ms_1(1)	;
	//EN_PORT |= 0x01 ;			/* Set LCD Enable = "1"  */
	//delay_ms_1(dly);
	//EN_PORT &= 0xFE ;			/* Set LCD Enable = "0"  */
	//delay_ms_1(dly);
//
	//DATA_PORT = 0x06 ;			/* 5b. */
	//delay_ms_1(1)	;
	//EN_PORT |= 0x01 ;			/* Set LCD Enable = "1"  */
	//delay_ms_1(dly);
	//EN_PORT &= 0xFE ;			/* Set LCD Enable = "0"  */
	//delay_ms_1(dly);
	////HALT
	///*-----------Write "H"------------------------------------*/
	////Port_WR_Parallel (0x48, RS_Data)
	////DATA_PORT = (1 << RS_bit) ;	/* Set RS = 1 */
	//DATA_PORT = 0x14 ;			/* 6a.  Set RS = 1 */
	//delay_ms_1(1)	;
	//EN_PORT |= 0x01 ;			/* Set LCD Enable = "1"  */
	//delay_ms_1(dly);
	//EN_PORT &= 0xFE ;			/* Set LCD Enable = "0"  */
	//delay_ms_1(dly);
	//
	//DATA_PORT = 0x18 ;			/* 6b. 0001 0100 - Set RS = 1*/
	//delay_ms_1(1)	;
	//EN_PORT |= 0x01 ;			/* Set LCD Enable = "1"  */
	//delay_ms_1(dly);
	//EN_PORT &= 0xFE ;			/* Set LCD Enable = "0"  */
	//delay_ms_1(dly);
	///*-------------Write "I"------------------------------------*/
	//DATA_PORT = 0x14 ;			/* 7a. Set RS = 1*/
	//delay_ms_1(1)	;
	//EN_PORT |= 0x01 ;			/* Set LCD Enable = "1"  */
	//delay_ms_1(dly);
	//EN_PORT &= 0xFE ;			/* Set LCD Enable = "0"  */
	//delay_ms_1(dly);
	//
	//DATA_PORT = 0x19 ;			/* 7b. 0001 1001 -Set RS = 1*/
	//delay_ms_1(1)	;
	//EN_PORT |= 0x01 ;			/* Set LCD Enable = "1"  */
	//delay_ms_1(dly);
	//EN_PORT &= 0xFE ;			/* Set LCD Enable = "0"  */
	//delay_ms_1(dly);
	///*--------------Write "T"----------------------------------*/
	//DATA_PORT = 0x15 ;			/* 8a. Set RS = 1*/
	//delay_ms_1(1)	;
	//EN_PORT |= 0x01 ;			/* Set LCD Enable = "1"  */
	//delay_ms_1(dly);
	//EN_PORT &= 0xFE ;			/* Set LCD Enable = "0"  */
	//delay_ms_1(dly);
	//
	//DATA_PORT = 0x14 ;			/* 8b. 0001 0100 Set RS = 1*/
	//delay_ms_1(1)	;
	//EN_PORT |= 0x01 ;			/* Set LCD Enable = "1"  */
	//delay_ms_1(dly);
	//EN_PORT &= 0xFE ;			/* Set LCD Enable = "0"  */
	//delay_ms_1(dly);
//
	////HALT
//}
//#endif

/****************************************************************
void LCDGoto(char x, char y){...}
void LCDcmd(char k){...}
void charLCD(char a){...}
****************************************************************/
//#include <avr/io.h>
//#include <util/delay.h>
//#include <avr/pgmspace.h>
//
//char i, b=0x40, c=0x00,z,d,c,f;
//
//const char mycustomchar[] PROGMEM={ 0x0E, 0x11, 0x11, 0x0E, 0x04, 0x04, 0x0A, 0x0A,
												//0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xFF };//as in image above
//
//int main(void)
//{
	//char f;
	//DDRA=0xff;
	//DDRB=0xff;
	//cmd(0x01);// Clear the LCD Screen
	//
	//definechar(mycustomchar,0);/* function defined below-it assigns the value to character code. 
										//The character code I assign is '0' */
	//definechar(mycustomchar+8,1); /* function defined below-it assigns the value to 
											//character code. The character code I assign is '1'*/	
	//while(1)
	//{
		//LCDcmd(0x0c);
		//LCDcmd(0x38);// 2lines 5x7 matrix, 8 bit mode
		//LCDcmd(0x80);// beginning of first line
		//LCDGoto(0,1);// 2nd row, first column
		//sprintf(f,"%c",0);// print 0 as a character
		//charLCD(f); // print the character
		//LCDGoto(1,1);// 2nd row 2nd column
		//sprintf(f,"%c",1);// print '1' as a character
		//charLCD(f);// print the character
	//}
//}
//
//void definechar(const char *pc, char char_code)
//{
	//char a, pcc;
	//int i;
	//
	//a=(char_code<<3)|0x40;
//
	//for (i=0; i<8; i++)
	//{
		//pcc=pgm_read_word(&pc[i]);
		//LCDcmd(a); a++;
		//charLCD(pcc);
	//}
//}

// Switch LCD on
void lcd_on(void)
{
	PinWR(LCD_SW, &PORTC, 0) ;
}