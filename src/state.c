#include "types.h"
#include "system.h"

#include "modem.h"
#include "power.h"
#include "temperature.h"
#include "door.h"
#include "console.h"

#include "state.h"

static State state_current;
static State state_previous;
static uint8_t state_flags;

// limits set on state_init
static int16_t limit_temperature_min;
static int16_t limit_temperature_max;

static void state_update_power();
static void state_check_power_mains(void);
static void state_check_power_battery(void);
static void state_update_temperature(void);
static void state_update_door(void);

/*
 * @brief Initialize the state module with defined limits
 *
 * @param min_temp The minimum temperature
 * @param max_temp The maximum temperature 
 * 
 */
void state_init(int16_t min_temp, int16_t max_temp)
{
    limit_temperature_min = min_temp;
    limit_temperature_max = max_temp;
    state_reset();
}

/*
 * @brief Resets the state module, but retains the configured limits
 */
void state_reset(void)
{
    memset(&state_current, 0, sizeof(state_current));
    memset(&state_previous, 0, sizeof(state_previous));
    state_flags = 0;
}

/*
 * @brief Prints the current state to the console
 */
void state_print(void)
{
	console_writeline("Current state:");
	console_writeline("-----------------------------------------");
	console_writeline("Mains: %sV", power_get_mains_string());
	console_writeline("Battery: %sV", power_get_battery_string());
	console_writeline("Temperature: %s", temperature_get_string());
	console_writeline("Door: %s%s",
		state_door_is_open() ? "Open" : "Closed",
		state_door_changed() ? " (changed)" : "");
	console_writeline("Signal Strength: %d", state_get_signal_strength());
}

/*
 * @brief Gets the current state
 *
 * @return Pointer to the current state
 */
inline const State *state_get_current(void)
{
    return &state_current;
}

/*
 * @brief Checks if the STATE_MAINS_LOW flag is set
 *
 * @return true if STATE_MAINS_LOW is set, false otherwise
 */
inline bool state_power_mains_is_low(void)
{
    return (state_flags & STATE_MAINS_LOW) == STATE_MAINS_LOW;
}

/*
 * @brief Checks if the STATE_BATTERY_LOW flag is set
 *
 * @return true if STATE_BATTERY_LOW is set, false otherwise
 */
inline bool state_power_battery_is_low(void)
{
    return (state_flags & STATE_BATTERY_LOW) == STATE_BATTERY_LOW;
}

/*
 * @brief Checks if the STATE_TEMP_LOW flag is set
 *
 * @return true if STATE_TEMP_LOW is set, false otherwise
 */
inline bool state_temperatue_is_low(void)
{
    return (state_flags & STATE_TEMP_LOW) == STATE_TEMP_LOW;
}

/*
 * @brief Checks if the STATE_TEMP_HIGH flag is set
 *
 * @return true if STATE_TEMP_HIGH is set, false otherwise
 */
inline bool state_temperature_is_high(void)
{
    return (state_flags & STATE_TEMP_HIGH) == STATE_TEMP_HIGH;
}

/*
 * @brief Checks if the STATE_DOOR_OPEN flag is set
 *
 * @return true if STATE_DOOR_OPEN is set, false otherwise
 */
inline bool state_door_is_open(void)
{
    return (state_flags & STATE_DOOR_OPEN) == STATE_DOOR_OPEN;
}

/*
 * @brief Checks if the door state changed since last update
 *
 * @return true if STATE_DOOR_CHANGED is set, false otherwise
 */
inline bool state_door_changed(void)
{
    return (state_flags & STATE_DOOR_CHANGED) == STATE_DOOR_CHANGED;
}

/*
 * @brief Updates the current state
 */
void state_update(void)
{
    memcpy(&state_previous, &state_current, sizeof(state_previous));
    state_update_power();
    state_update_temperature();
    state_update_door();
}

/*
 * @brief Updates the signal strength
 */
inline void state_update_signal_strength(uint8_t signal_strength)
{
    state_current.signal_strength = signal_strength;
}

/*
 * @brief Gets the signal strength as a string
 *
 * @return Pointer to the signal strength string
 */

inline uint8_t state_get_signal_strength(void)
{
    return state_current.signal_strength;
}

/*
 * @brief Updates the power state
 * 
 * @details Updates the State struct with the current power state
 */
static void state_update_power()
{
    memcpy(&state_current.power, power_get(), sizeof(state_current.power));
    state_check_power_mains();
    state_check_power_battery();
}

/*
 * @brief Checks the mains power against the limits
 */
static void state_check_power_mains(void)
{
    if (state_current.power.mains < MAIN_12VIN_MIN)
    {
        state_flags |= STATE_MAINS_LOW;
    }
    else
    {
        state_flags &= ~STATE_MAINS_LOW;
    }
}

/*
 * @brief Checks the battery power against the limits
 */
static void state_check_power_battery(void)
{
    if (state_current.power.battery < VBATT_LOW_LIMT && 
        state_current.power.mains < MAIN_9VMAIN_IN)
    {
        state_flags |= STATE_BATTERY_LOW;
    }
    else
    {
        state_flags &= ~STATE_BATTERY_LOW;
    }
}

/*
 * @brief Updates and checks the temperature against the limits
 */
static void state_update_temperature(void)
{
    memcpy(&state_current.temperature, temperature_get(), sizeof(state_current.temperature));
    if (state_current.temperature < limit_temperature_min)
    {
        state_flags |= STATE_TEMP_LOW;
    }
    else
    {
        state_flags &= ~STATE_TEMP_LOW;
    }

    if (state_current.temperature > limit_temperature_max)
    {
        state_flags |= STATE_TEMP_HIGH;
    }
    else
    {
        state_flags &= ~STATE_TEMP_HIGH;
    }
}

/*
 * @brief Updates the door state and detects changes
 */
static void state_update_door(void)
{
    bool current_door_state = door_is_open();
    state_current.is_door_open = current_door_state;
    
    // Check if door state changed from previous state
    if (current_door_state != state_previous.is_door_open)
    {
        state_flags |= STATE_DOOR_CHANGED;
    }
    else
    {
        state_flags &= ~STATE_DOOR_CHANGED;
    }
    
    // Set/clear door open flag
    if (current_door_state)
    {
        state_flags |= STATE_DOOR_OPEN;
    }
    else
    {
        state_flags &= ~STATE_DOOR_OPEN;
    }
}