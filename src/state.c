#include "types.h"
#include "system.h"

#include "power.h"
#include "temperature.h"
#include "door.h"
#include "console.h"

#include "state.h"

static State state_current;
static uint8_t state_flags;

// limits set on state_init
static int16_t limit_temperature_min;
static int16_t limit_temperature_max;

static void state_update_power();
static void state_check_power_mains(void);
static void state_check_power_battery(void);
static void state_update_temperature(void);
static void state_update_door(void);
static void state_update_signal_strength(void);

/*
 * @brief Initialize the state module with defined limits
 *
 * @param min_temp The minimum temperature
 * @param max_temp The maximum temperature 
 * 
 */
void state_init(int16_t min_temp, int16_t max_temp)
{
    limit_temperature_min = min_temp;
    limit_temperature_max = max_temp;
    state_reset();
    state_update();
}

/*
 * @brief Resets the state module, but retains the configured limits
 */
void state_reset(void)
{
    memset(&state_current, 0, sizeof(state_current));
    state_flags = 0;
}

/*
 * @brief Updates the current state and compares it to the previous saved state.
 *
 * @return 1 if the state has changed, 0 otherwise
 */
int state_changed(void)
{
    uint8_t state_flags_saved = state_flags;
    state_update();
    return state_flags != state_flags_saved;
}

/*
 * @brief Gets the current state
 *
 * @return Pointer to the current state
 */
const State *state_get_current(void)
{
    return &state_current;
}

/*
 * @brief Checks if the STATE_MAINS_LOW flag is set
 *
 * @return 1 if STATE_MAINS_LOW is set, 0 otherwise
 */
bool state_power_mains_is_low(void)
{
    return (state_flags & STATE_MAINS_LOW) == STATE_MAINS_LOW;
}

/*
 * @brief Checks if the STATE_BATTERY_LOW flag is set
 *
 * @return 1 if STATE_BATTERY_LOW is set, 0 otherwise
 */
bool state_power_battery_is_low(void)
{
    return (state_flags & STATE_BATTERY_LOW) == STATE_BATTERY_LOW;
}

/*
 * @brief Checks if the STATE_TEMP_LOW flag is set
 *
 * @return 1 if STATE_TEMP_LOW is set, 0 otherwise
 */
bool state_temperatue_is_low(void)
{
    return (state_flags & STATE_TEMP_LOW) == STATE_TEMP_LOW;
}

/*
 * @brief Checks if the STATE_TEMP_HIGH flag is set
 *
 * @return 1 if STATE_TEMP_HIGH is set, 0 otherwise
 */
bool state_temperature_is_high(void)
{
    return (state_flags & STATE_TEMP_HIGH) == STATE_TEMP_HIGH;
}

/*
 * @brief Checks if the STATE_DOOR_OPEN flag is set
 *
 * @return 1 if STATE_DOOR_OPEN is set, 0 otherwise
 */
bool state_door_is_open(void)
{
    return (state_flags & STATE_DOOR_OPEN) == STATE_DOOR_OPEN;
}


/*
 * @brief Updates the current state
 */
void state_update(void)
{
    state_update_power();
    state_update_temperature();
    state_update_door();
    state_update_signal_strength();
}

/*
 * @brief Updates the power state
 * 
 * @details Updates the State struct with the current power state
 */
static void state_update_power()
{
    memcpy(&state_current.power, power_get(), sizeof(state_current.power));
    state_check_power_mains();
    state_check_power_battery();
}

/*
 * @brief Checks the mains power against the limits
 */
static void state_check_power_mains(void)
{
    if (state_current.power.mains < MAIN_12VIN_MIN)
    {
        state_flags |= STATE_MAINS_LOW;
    }
    else
    {
        state_flags &= ~STATE_MAINS_LOW;
    }
}

/*
 * @brief Checks the battery power against the limits
 */
static void state_check_power_battery(void)
{
    if (state_current.power.battery < VBATT_LOW_LIMT && 
        state_current.power.mains < MAIN_9VMAIN_IN)
    {
        state_flags |= STATE_BATTERY_LOW;
    }
    else
    {
        state_flags &= ~STATE_BATTERY_LOW;
    }
}

/*
 * @brief Updates the signal strength
 */
static void state_update_signal_strength(void)
{
    // This function is a placeholder for signal strength updates.
    // In a real implementation, it would read from a signal strength source.
    // For now, we set it to a fixed value.
    state_current.signal_strength = 99; // Example fixed value, replace with actual logic if needed
}

/*
 * @brief Updates and checks the temperature against the limits
 */
static void state_update_temperature(void)
{
    memcpy(&state_current.temperature, temperature_get(), sizeof(state_current.temperature));
    if (state_current.temperature < limit_temperature_min)
    {
        state_flags |= STATE_TEMP_LOW;
    }
    else
    {
        state_flags &= ~STATE_TEMP_LOW;
    }

    if (state_current.temperature > limit_temperature_max)
    {
        state_flags |= STATE_TEMP_HIGH;
    }
    else
    {
        state_flags &= ~STATE_TEMP_HIGH;
    }
}

/*
 * @brief Updates the door state
 */
static void state_update_door(void)
{
    state_current.is_door_open = door_is_open();
    
    if (state_current.is_door_open)
    {
        state_flags |= STATE_DOOR_OPEN;
    }
    else
    {
        state_flags &= ~STATE_DOOR_OPEN;
    }
}