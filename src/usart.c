/*------------------------------------------------------------------------------
                                    INCLUDES
------------------------------------------------------------------------------*/
#include <stdint.h>
#include <avr/io.h>
#include <avr/interrupt.h>
#include <stdlib.h>
#include <stdarg.h>
#include <stdio.h>
#include <string.h>

#include "types.h"
#include "delay.h"
#include "system.h"
#include "io_pins.h"
#include "timer.h"
#include "usart.h"
#include "hexdump.h"
#include "console.h"

/*------------------------------------------------------------------------------
                                    DEFINES
------------------------------------------------------------------------------*/
#define UART0_RECEIVE_INTERRUPT   USART_RX_vect 
#define UART0_TRANSMIT_INTERRUPT  USART_UDRE_vect 

volatile static int UART_RxPtr = 0;
volatile static char UART_RxBuf[UART_RX_BUFFER_SIZE];

#define MAX_LINES	16
#define MAX_CHARS	256
static char LineBuffer[MAX_LINES][MAX_CHARS];
static int WriteLine = 0;
static int WriteChar = 0;
static int ReadLine = 0;

/*-----------------------------------------------------------
  Function   : usart0_init
  Purpose    : Initiates the USART, set the BR etc.
  Parameters : Integer -  UBRR (Baud rate)
  Returns    : Nothing
  Notes      : Async Mode. Enables global Interrupt service
					(PRR.PRUSARTn) must be written to '0' in order to 
					enable USARTn. USART 0 and 1 are in PRR0, and 
					USART 2 is in PRR2.
------------------------------------------------------------*/
void usart0_init( uint8_t ubrr)
{
	unsigned char cSREG;

	cSREG = SREG;			/* store SREG value */
	asm("cli");
	
	/*Set Asynchronous mode */
	UCSR0C &= ~(1<<UMSEL01);
	UCSR0C &= ~(1<<UMSEL00);	
	
	/*Set baud rate 
	----------------*/
	UCSR0A = (1<<U2X0);		/* Enable 2x speed */
	//ubrr =  416;				/* Fix for 4800 bps-U2X0=1 */
	//ubrr =  16;				/* Set for 115200 bps  */
	//ubbr = 207 ;				/* Baudrate = 9600*/
	UBRR0H = (unsigned char)(ubrr>>8);
	UBRR0L = (unsigned char)ubrr;

	/* Set frame format: 8data, 2stop bit */
	//UCSR0C = (1<<USBS0)|(3<<UCSZ00);	
	/* Set frame format: 8data, 1stop bit */
	UCSR0C &= ~(1<<USBS0);		/* 1stop bit */
		
	UCSR0B &= ~(1<<UCSZ02);		/* 8 data bits 'CRB'*/
	UCSR0C = (3<<UCSZ00);		/* 8 data bits 'CRC'*/

	UCSR0C &= ~(1<<UPM00);
	UCSR0C &= ~(1<<UPM01);		/* NO parity bits */
	UCSR0C &= ~(1<<UCPOL0);		/* Async set to 0 */

	/*Enable receiver and transmitter & Rx interrupt */
	UCSR0B |= (1<<RXEN0)|(1<<TXEN0)|(1<<RXCIE0);

	SREG = cSREG;			/* restore SREG value (I-bit) */
}

/*-----------------------------------------------------------
  Function   : usart0_transmit
  Purpose    : USART transmit function based on polling of
					the Data Register Empty (UDRE0) Flag
  Parameters : Character -  data
  Returns    : Nothing
  Notes      :
------------------------------------------------------------*/
void usart0_transmit( unsigned char data )
{
	/* Wait for empty transmit buffer */
	while ( !( UCSR0A & (1<<UDRE0)) ) { asm("nop"); }
	
	/* Put data into buffer, to send it */
	UDR0 = data;
}

/*-----------------------------------------------------------
  Function   : usart0_send_string
  Purpose    : Sends a string.
  Parameters : String pointer
  Returns    : Nothing
  Notes      :
------------------------------------------------------------*/
void usart0_send_string(char *str)					/* Send string of USART data function */
{
	uint8_t i=0;
	while (str[i]!=0)
	{
		usart0_transmit(str[i]);						/* Send each char of string till the NULL */
		i++;
	}
}

/*-----------------------------------------------------------
  Function   : usart0_receive
  Purpose    : Checks for a UART Rx'ed character.
  Parameters : Contents of RxD register
  Returns    : Nothing
  Notes      :
------------------------------------------------------------*/
unsigned char usart0_receive( void )
{
	/* Wait for data to be received */
	while ( !(UCSR0A & (1<<RXC0)) )	{;}

	if ((UCSR0A & (1<<FE0)))
	{
		// Frame error
		return 0;
	}

	if ((UCSR0A & (1<<DOR0)))
	{
		// RxBuff overrun
		return 0;
	}

	if ((UCSR0A & (1<<UPE0)))
	{
		// Parity error
		return 0;
	}

	/* Get and return received data from buffer */
	return UDR0;
}

/*------------------------------------------------------------------
Note: RXC bit in UCSR0A register.
-----
USART Control and Status Register 0 A - "UCSR0A"
RXC0 bit:- USART Receive Complete.

The RXC0 flag bit = 0: when the receive buffer (UDR00) is empty and clear.
If the Receiver is disabled, the receive buffer will be
flushed and consequently the RXC0 bit will become zero. 
The RXC0 Flag can be used to generate a "Receive Complete interrupt".
-------------------------------------------------------------------*/
void usart0_flush( void )
{
	while ( (UCSR0A & (1<<RXC0)) ) {(void)UDR0;}
	return;
}

/***************************************************
  Function   : usart0_rx_disable
  Purpose    :  Disable receiver & Rx interrupt
  Parameters : 
  Returns    : Nothing
  Notes      :
****************************************************/
void usart0_rx_disable(void)
{
	UCSR0B &= ~(1<<RXEN0) ;
	UCSR0B &= ~(1<<RXCIE0);
}

/***************************************************
  Function   : usart0_rx_enable
  Purpose    : Enable receiver & Rx interrupt
  Parameters : 
  Returns    : Nothing
  Notes      :
****************************************************/
void usart0_rx_enable(void)
{
	UCSR0B |= (1<<RXEN0)|(1<<RXCIE0) ;
}

/*************************************************************
USART1:
-------
**************************************************************
  Function   : usart1_init
  Purpose    : Initiates the USART, set the BR etc.
  Parameters : Integer -  UBRR (Baud rate)
  Returns    : Nothing
  Notes      : Async Mode. Enables global Interrupt service
------------------------------------------------------------*/
void usart1_init(uint8_t ubrr)
{
	unsigned char cSREG;
	cSREG = SREG;			/* store SREG value */
	asm("cli");
	
	// Set Asynchronous mode
	UCSR1C &= ~(1<<UMSEL11);
	UCSR1C &= ~(1<<UMSEL10);	
	
	// Set baud rate 
	UCSR1A = (1<<U2X1);			/* Enable 2x speed */
	ubrr =  BR115200;			/* Set for 115200 bps @11.0592Mhz  */
	UBRR1H = (uint8_t)(ubrr>>8);
	UBRR1L = (uint8_t)ubrr;
	
	/* Set frame format: 8data, 1stop bit */
	UCSR1C &= ~(1<<USBS1);		/* 1stop bit */
		
	UCSR1B &= ~(1<<UCSZ12);		/* 8 data bits 'CRB'*/
	UCSR1C = (3<<UCSZ10);			/* 8 data bits 'CRC'*/

	UCSR1C &= ~(1<<UPM10);
	UCSR1C &= ~(1<<UPM11);		/* NO parity bits */
	UCSR1C &= ~(1<<UCPOL1);		/* Async set to 0 */

	/*Enable receiver and transmitter & Rx interrupt */
	//UCSR1B |= (1<<RXEN)|(1<<TXEN)|(1<<RXCIE);
	//UCSR1B |= (1<<TXEN)|(1<<TXCIE) ;
	UCSR1B |= (1<<TXEN1) ;

	SREG = cSREG;			/* restore SREG value (I-bit) */
}

/***********************************************************
A UART Transmit Complete interrupt will be
generated only if the TXCIE bit is written to one.
************************************************************/
 ISR (USART1_TX_vect)
 {
	asm("nop");
 }

/*-----------------------------------------------------------
  Function   : usart1_send_char
  Purpose    : USART transmit function based on polling of
					the Data Register Empty (UDRE2) Flag
  Parameters : Character -  data
  Returns    : Nothing
  Notes      :
------------------------------------------------------------*/
void usart1_send_char( char data )
{
	/* Wait for empty transmit buffer */
	while ( !( UCSR1A & (1<<UDRE1)) ) {;}
			
	/* Put data into buffer, sends the data */
	UDR1 = data;
}

/*-----------------------------------------------------------
  Function   : usart1_send_raw_string
  Purpose    : Sends a string.
  Parameters : String pointer
  Returns    : Nothing
  Notes      :
------------------------------------------------------------*/
void usart1_send_raw_string(char *str)
{
	uint8_t i=0;

	while (str[i]!=0)
	{
		usart1_send_char(str[i]);
		i++;
	}
}

void usart1_send_string(const char *format, ...)
{
    char buffer[256]; // Adjust the size as needed
    va_list args;
    va_start(args, format);
    vsnprintf(buffer, sizeof(buffer), format, args);
    va_end(args);

    // Assuming you have a function to send a string via USART
    usart1_send_raw_string(buffer);
}


/*------------------------------------------------------------------------------
  Function   : ISR(UART0_RECEIVE_INTERRUPT)
  Purpose    : Facilitates interrupt driven USART receive
  Parameters : None
  Returns    : Nothing
  Notes      : USART receive interrupt handler.  Interrupt handler gets
					called once data is received from the host
------------------------------------------------------------------------------*/
//TODO - Why save SREG, and why enabe interrupts?
ISR(USART0_RX_vect)
{
	unsigned char cSREG = SREG;	
	asm("cli");

	UART_RxBuf[UART_RxPtr++] = UDR0;	
	if (UART_RxPtr >= UART_RX_BUFFER_SIZE)	
	{
		UART_RxPtr = 0;
	}

	SREG = cSREG;
}

void usart0_reset_buffer(void)
{
	WriteLine = 0;
	WriteChar = 0;
	ReadLine = 0;
}

uint8_t usart0_get_buffer_size(void)
{
	return UART_RxPtr;
}

void usart0_pack_data(void)
{
	uint8_t data;
	uint8_t size;
	unsigned char *buffer;

	// There is nothing in the buffer
	if (UART_RxPtr == 0)
		return;

	usart1_send_string("Packing %d bytes data:\r\n", UART_RxPtr);

    // Copy data while interrupts are disabled, then re-enable them
	asm("cli");
	size = UART_RxPtr;
	buffer = malloc(size);
	memcpy(buffer, (const void *)UART_RxBuf, size);
	UART_RxPtr = 0 ;	/* Zero ptr to fill UART Rx buffer from start*/
	asm("sei");

	//HexDump("DATA:", buffer, size);

	for (int i = 0; i < size; i++)
	{
		data = buffer[i];
		LineBuffer[WriteLine][WriteChar] = data;

		// Ignore newlines ...
		 if (data == '\n')
			continue;

		if (data == '\r')
		{
			// Don't add the CR character to the buffer
			LineBuffer[WriteLine][WriteChar] = '\0';

			// Start a new line
			WriteLine++;
			if (WriteLine == MAX_LINES)
				WriteLine = 0;

			WriteChar = 0;
		}
		else
		{
			WriteChar++;
			if (WriteChar == MAX_CHARS)
			{
				// Line buffer overrun
				WriteChar = 0;	
			}
		}
	}
	free(buffer);

	usart0_hex_dump_buffer();
}

void usart0_hex_dump_buffer(void)
{
	int l;
	char str[24];
	
	console_write("Write (%02d,%02d) ", WriteLine, WriteChar);
	console_writeline("Read (%02d,%02d)\r\n", ReadLine, 0);

    l = ReadLine;
	while (l != WriteLine)
	{
		if ((l != WriteLine) || (WriteChar > 0))
		{
			snprintf(str, sizeof(str), "Line %02d: ", l);
			hex_dump(str, LineBuffer[l], strlen(LineBuffer[l]));
		}

		if (++l == MAX_LINES)
			l = 0;
	}

	console_writeline("---");
}

char *usart0_get_next_line(void)
{
	usart0_pack_data();

	// No new lines to read
	if (ReadLine == WriteLine)
		return NULL;

	// Return a pointer to the line to avoid excessive memcpy operations
	return &LineBuffer[ReadLine][0];
}

void usart0_move_next_line()
{
	// Only move of there is unread data in the buffer
	if (ReadLine != WriteLine)
	{
		ReadLine++;
		if (ReadLine == MAX_LINES)
			ReadLine = 0;
	}
}

uint8_t usart0_compare_lines(char *buffer, char *expected)
{
	uint8_t i = 0;
	uint8_t e = 0;

	while (i < strlen(buffer))
	{
		if (buffer[i++] == expected[e])
		{
			e++;
		if (e == strlen(expected))
		{
			usart1_send_string("Found %s at (%02d,%02d)\r\n", expected, ReadLine, i);
			return true;
		}
		}
		else
		{
			e = 0;
		}
	}

	return false;
}

bool usart0_wait_for_string_with_data(char *expected, char *data, uint8_t maxDataSize)
{
	char *lineBuffer;
	uint8_t dataSize = 0;
	uint16_t delay = 100;
	uint16_t max_retries = 60;
	uint16_t retry = 0;

	while (retry++ < max_retries)
	{
		// Give some time for the buffer to fill up
		delay_ms(delay);

		if ((lineBuffer = usart0_get_next_line()) == NULL)
		{
			usart1_send_char('.');
			delay_ms(delay);
			continue;
		}

		if (usart0_compare_lines(lineBuffer, expected))
		{
			usart0_move_next_line();
			return true;
		}
		else
		{
			if (usart0_compare_lines(lineBuffer, "+CME ERROR"))
				return false;

			// Copy the data
			if (dataSize < maxDataSize)
			{
				uint8_t remaining = maxDataSize - dataSize;
				if (strlen(lineBuffer) <= remaining)
					remaining = strlen(lineBuffer);

				memcpy(&data[dataSize], lineBuffer, remaining);
				dataSize += remaining;
			}
		}

		usart0_move_next_line();
	}

	usart1_send_string("WaitForString failed - Max retries reached!\r\n");
	return false;

}

bool usart0_wait_for_string(char *expected)
{
	return usart0_wait_for_string_with_data(expected, NULL, 0);
}

bool usart0_send_command(char *command, char *expected)
{
	usart1_send_string("CMD: [%s] [%s]\r\n", command, expected);

	// Get all remaining data from the modem
	// usart0_pack_data();

    // Only write a command if there is one
    if (strlen(command) > 0)
	{
		usart0_send_string(command);
		usart0_send_string("\r");
		
		// Wait for the modem to echo the command back
		if (usart0_wait_for_string(command) == false)
		{
			usart1_send_string("Send command failed.\r\n");
			return false;
		}
	}

	if (strlen(expected) == 0)
		return true;

	if (usart0_wait_for_string(expected) == false)
	{
		usart1_send_string("Expected result failed.\r\n");
		return false;
	}

	return true;
}

bool usart0_read_data(char *command, char *expected, char *data, uint8_t maxDataSize)
{
	usart1_send_string("DATA: [%s] [%s]\r\n", command, expected);

	// Get all remaining data from the modem
	// usart0_pack_data();

    // Only write a command if there is one
    if (strlen(command) > 0)
	{
		usart0_send_string(command);
		usart0_send_string("\r");
		
		// Wait for the modem to echo the command back
		if (usart0_wait_for_string(command) == false)
		{
			usart1_send_string("Send command failed.\r\n");
			return false;
		}
	}

	if (strlen(expected) == 0)
		return true;

	if (usart0_wait_for_string_with_data(expected, data, maxDataSize) == false)
	{
		usart1_send_string("Expected result failed.\r\n");
		return false;
	}

	return true;
}

uint8_t usart0_get_unsolicited_data(char *buffer, uint16_t maxSize)
{
	char *lineBuffer = NULL;
	uint16_t remaining;

	while (1)
	{
		if ((lineBuffer = usart0_get_next_line()) == NULL)
			return 0;

		if (lineBuffer[0] != '+')
		{
			usart0_move_next_line();
			continue;
		}

		break;
	}

	remaining = strlen(lineBuffer);
	if (remaining >= maxSize-1)
		remaining = maxSize-1; 
	memcpy(buffer, lineBuffer, remaining);
	buffer[remaining] = '\0';
	usart0_move_next_line();

	return remaining;
}