/*------------------------------------------------------------------------------
                                    INCLUDES
------------------------------------------------------------------------------*/
#include <stdint.h>
#include <avr/io.h>
#include <avr/interrupt.h>
#include <stdlib.h>

#include "types.h"
#include "delay.h"
#include "system.h"
#include "lcd_def.h"
#include "lcd.h"
#include "interrupts.h"
#include "io_pins.h"
#include "timer.h"
#include "usart.h"
#include "hexdump.h"
#include "console.h"

/*------------------------------------------------------------------------------
                                    DEFINES
------------------------------------------------------------------------------*/
/* ATmega328 & 324 - USART */
//#define ATMEGA_USART0
#define UART0_RECEIVE_INTERRUPT   USART_RX_vect 
#define UART0_TRANSMIT_INTERRUPT  USART_UDRE_vect 
#define UART0_STATUS   UCSR0A
#define UART0_CONTROL  UCSR0B
#define UART0_DATA     UDR0
#define UART0_UDRIE    UDRIE0

/*------------------------------------------------------------------------------
                                    EXTERNAL VARIABLES
------------------------------------------------------------------------------*/
//extern UI_8 sysinit ;
extern UI_8 Modem_started  ;
/*------------------------------------------------------------------------------
                                    LOCAL FUNCTIONS PROTOTYPES
------------------------------------------------------------------------------*/
void USART0_Init( UI_8 ubrr);
void USART0_Transmit( unsigned char data );
unsigned char USART0_Receive( void ) ;
void USART0_Flush( void );
void USART_ERROR_HANDLER(void);
void USART0_sendINT( UI_8  data );
void USART0_SendString(char *str) ;
void USART1_Tx_UI_8_dec( uint8_t val );
void USART1_Tx_UI_16_hex( uint16_t val );

/*------------------------------------------------------------------------------
                                    LOCAL STATIC VARIABLES
------------------------------------------------------------------------------*/
/* Global variable definitions:
-------------------------------*/
/* Global Variables used in ISR must be type volatile */

volatile int UART_RxPtr = 0;
volatile char UART_RxBuf[UART_RX_BUFFER_SIZE];

 
#define MAX_LINES	16
#define MAX_CHARS	256
char LineBuffer[MAX_LINES][MAX_CHARS];
int WriteLine = 0;
int WriteChar = 0;
int ReadLine = 0;

/*-----------------------------------------------------------
  Function   : USART0_Init
  Purpose    : Initiates the USART, set the BR etc.
  Parameters : Integer -  UBRR (Baud rate)
  Returns    : Nothing
  Notes      : Async Mode. Enables global Interrupt service
					(PRR.PRUSARTn) must be written to '0' in order to 
					enable USARTn. USART 0 and 1 are in PRR0, and 
					USART 2 is in PRR2.
------------------------------------------------------------*/
void USART0_Init( UI_8 ubrr)
{
	//unsigned char cSREG;

	//cSREG = SREG;			/* store SREG value */
	asm("cli");
	
	/*Set Asynchronous mode */
	UCSR0C &= ~(1<<UMSEL01);
	UCSR0C &= ~(1<<UMSEL00);	
	
	/*Set baud rate 
	----------------*/
	UCSR0A = (1<<U2X0);		/* Enable 2x speed */
	//ubrr =  416;				/* Fix for 4800 bps-U2X0=1 */
	//ubrr =  16;				/* Set for 115200 bps  */
	//ubbr = 207 ;				/* Baudrate = 9600*/
	UBRR0H = (unsigned char)(ubrr>>8);
	UBRR0L = (unsigned char)ubrr;

	/* Set frame format: 8data, 2stop bit */
	//UCSR0C = (1<<USBS0)|(3<<UCSZ00);	
	/* Set frame format: 8data, 1stop bit */
	UCSR0C &= ~(1<<USBS0);		/* 1stop bit */
		
	UCSR0B &= ~(1<<UCSZ02);		/* 8 data bits 'CRB'*/
	UCSR0C = (3<<UCSZ00);		/* 8 data bits 'CRC'*/

	UCSR0C &= ~(1<<UPM00);
	UCSR0C &= ~(1<<UPM01);		/* NO parity bits */
	UCSR0C &= ~(1<<UCPOL0);		/* Async set to 0 */

	/*Enable receiver and transmitter & Rx interrupt */
	UCSR0B |= (1<<RXEN0)|(1<<TXEN0)|(1<<RXCIE0);
	//SREG = cSREG;			/* restore SREG value (I-bit) */
	asm("sei");
}
/*-----------------------------------------------------------
  Function   : USART0_Transmit
  Purpose    : USART transmit function based on polling of
					the Data Register Empty (UDRE0) Flag
  Parameters : Character -  data
  Returns    : Nothing
  Notes      :
------------------------------------------------------------*/
void USART0_Transmit( unsigned char data )
{
	/* Wait for empty transmit buffer */
	while ( !( UCSR0A & (1<<UDRE0)) ) {;}
	
	/* Put data into buffer, sends the data */
	UDR0 = data;
}
/*-----------------------------------------------------------
  Function   : USART0_sendINT
  Purpose    : USART transmit function based on polling of
					the Data Register Empty (UDRE0) Flag
  Parameters : Integer -  data
  Returns    : Nothing
  Notes      :
------------------------------------------------------------*/
void USART0_sendINT( UI_8 data )
{
	/* Wait for empty transmit buffer */
	while ( !( UCSR0A & (1<<UDRE0)) ) {;}
	
	/* Put data into buffer, sends the data */
	UDR0 = data;
}
/*-----------------------------------------------------------
  Function   : USART0_SendString
  Purpose    : Sends a string.
  Parameters : String pointer
  Returns    : Nothing
  Notes      :
------------------------------------------------------------*/
void USART0_SendString(char *str)					/* Send string of USART data function */
{
	uint8_t i=0;
	while (str[i]!=0)
	{
		USART0_Transmit(str[i]);						/* Send each char of string till the NULL */
		i++;
	}
}
/*-----------------------------------------------------------
  Function   : USART0_Receive
  Purpose    : Checks for a UART Rx'ed character.
  Parameters : Contents of RxD register
  Returns    : Nothing
  Notes      :
------------------------------------------------------------*/
unsigned char USART0_Receive( void )
{
	/* Wait for data to be received */
	while ( !(UCSR0A & (1<<RXC0)) )	{;}

	if ((UCSR0A & (1<<FE0)))
	{
		// Frame error
		return 0;
	}

	if ((UCSR0A & (1<<DOR0)))
	{
		// RxBuff overrun
		return 0;
	}

	if ((UCSR0A & (1<<UPE0)))
	{
		// Parity error
		return 0;
	}

	/* Get and return received data from buffer */
	return UDR0;
}
/*------------------------------------------------------------------
Note: RXC bit in UCSR0A register.
-----
USART Control and Status Register 0 A - "UCSR0A"
RXC0 bit:- USART Receive Complete.

The RXC0 flag bit = 0: when the receive buffer (UDR00) is empty and clear.
If the Receiver is disabled, the receive buffer will be
flushed and consequently the RXC0 bit will become zero. 
The RXC0 Flag can be used to generate a "Receive Complete interrupt".
-------------------------------------------------------------------*/
void USART0_Flush( void )
{
	while ( (UCSR0A & (1<<RXC0)) ) {(void)UDR0;}
	return;
}
/***************************************************
  Function   : USART0_RX_Disable
  Purpose    :  Disable receiver & Rx interrupt
  Parameters : 
  Returns    : Nothing
  Notes      :
****************************************************/
void USART0_RX_Disable(void)
{
	UCSR0B &= ~(1<<RXEN0) ;
	UCSR0B &= ~(1<<RXCIE0);
}
/***************************************************
  Function   : USART0_RX_Enable
  Purpose    : Enable receiver & Rx interrupt
  Parameters : 
  Returns    : Nothing
  Notes      :
****************************************************/
void USART0_RX_Enable(void)
{
	UCSR0B |= (1<<RXEN0)|(1<<RXCIE0) ;
}
/***************************************************
void USART_ERROR_DET(void)
****************************************************/
void USART_ERROR_HANDLER(void)
{
	UI_16 delay = 500;

	if (intrflags.UART_LastRxError == 1)
	{
		lcd_Clear();
		//lcd_SetCursorPos(0, 1);
		//lcd_Txt("Frame Error!", true);
		lcd_display_string("Frame Error!", 0, LCD_LINE_2);
		delay_ms_1(delay);
	}
	else if (intrflags.UART_LastRxError == 2)
	{
		lcd_Clear();
		//lcd_SetCursorPos(0, 1);
		//lcd_Txt("RxBuff overrun!", true);
		lcd_display_string("RxBuff overrun!", 0, LCD_LINE_2);
		delay_ms_1(delay);
	}
	else if (intrflags.UART_LastRxError == 3)
	{
		lcd_Clear();
		//lcd_SetCursorPos(0, 1);
		//lcd_Txt("Parity error!", true);
		lcd_display_string("Parity error!", 0, LCD_LINE_2);
		delay_ms_1(delay);
	}
	else intrflags.UART_LastRxError = 0;
	
	return;
}

/*************************************************************
USART1:
-------
**************************************************************
  Function   : USART1_Init
  Purpose    : Initiates the USART, set the BR etc.
  Parameters : Integer -  UBRR (Baud rate)
  Returns    : Nothing
  Notes      : Async Mode. Enables global Interrupt service
------------------------------------------------------------*/
void USART1_Init(UI_8 ubrr)
{
	asm("cli");
	
	// Set Asynchronous mode
	UCSR1C &= ~(1<<UMSEL11);
	UCSR1C &= ~(1<<UMSEL10);	
	
	// Set baud rate 
	UCSR1A = (1<<U2X1);			/* Enable 2x speed */
	ubrr =  BR115200;			/* Set for 115200 bps @11.0592Mhz  */
	UBRR1H = (UI_8)(ubrr>>8);
	UBRR1L = (UI_8)ubrr;
	
	/* Set frame format: 8data, 1stop bit */
	UCSR1C &= ~(1<<USBS1);		/* 1stop bit */
		
	UCSR1B &= ~(1<<UCSZ12);		/* 8 data bits 'CRB'*/
	UCSR1C = (3<<UCSZ10);			/* 8 data bits 'CRC'*/

	UCSR1C &= ~(1<<UPM10);
	UCSR1C &= ~(1<<UPM11);		/* NO parity bits */
	UCSR1C &= ~(1<<UCPOL1);		/* Async set to 0 */

	/*Enable receiver and transmitter & Rx interrupt */
	//UCSR1B |= (1<<RXEN)|(1<<TXEN)|(1<<RXCIE);
	//UCSR1B |= (1<<TXEN)|(1<<TXCIE) ;
	UCSR1B |= (1<<TXEN1) ;

	asm("sei");
}
/***********************************************************
A UART Transmit Complete interrupt will be
generated only if the TXCIE bit is written to one.
************************************************************/
 ISR (USART1_TX_vect)
 {
	//static UI_8 bit ;
//
	//bit = ~bit ;
	//PinWR(LED4 , &PORTA, bit) ;	
 }
/*-----------------------------------------------------------
  Function   : USART1_Send_char
  Purpose    : USART transmit function based on polling of
					the Data Register Empty (UDRE2) Flag
  Parameters : Character -  data
  Returns    : Nothing
  Notes      :
------------------------------------------------------------*/
void USART1_SendChar( char data )
{
	/* Wait for empty transmit buffer */
	while ( !( UCSR1A & (1<<UDRE1)) ) {;}
			
	/* Put data into buffer, sends the data */
	UDR1 = data;
}
/*-----------------------------------------------------------
  Function   : USART1_SendString
  Purpose    : Sends a string.
  Parameters : String pointer
  Returns    : Nothing
  Notes      :
------------------------------------------------------------*/
void USART1_SendRawString(char *str)
{
	uint8_t i=0;

	while (str[i]!=0)
	{
		USART1_SendChar(str[i]);
		i++;
	}
}

void USART1_SendString(const char *format, ...)
{
    char buffer[256]; // Adjust the size as needed
    va_list args;
    va_start(args, format);
    vsnprintf(buffer, sizeof(buffer), format, args);
    va_end(args);

    // Assuming you have a function to send a string via USART
    USART1_SendRawString(buffer);
}

/***********************************************************
Function:  Displays integers up to 127 as a string.
***********************************************************/
void USART1_Tx_int_8_dec( int8_t val )
{
	char buffer[5];
	itoa(val, buffer, 10);
	USART1_SendString(buffer);
}
/***********************************************************
Function:  Displays unsigned integers up to 255 as a string.
***********************************************************/
void USART1_Tx_UI_8_dec( uint8_t val )
{
	char buffer[4];
	itoa(val, buffer, 10);
	USART1_SendString(buffer);
}
/***********************************************************
Function:  Displays 16 bit integers as a string.
***********************************************************/
void USART1_Tx_UI_16_dec( uint16_t val )
{
	char buffer[6];
	itoa(val, buffer, 10);
	USART1_SendString(buffer);
}
/***********************************************************
Function:  Displays 32 bit integers as a string.
***********************************************************/
void USART1_Tx_UI_32_dec( uint32_t val )
{
	char buffer[11];
	itoa(val, buffer, 10);
	USART1_SendString(buffer);
}
/***********************************************************
Function:  Displays integers up to 255 as a hex value.
***********************************************************/
void USART1_Tx_UI_8_hex( uint8_t val )
{
	char buffer[5];
	snprintf(buffer, sizeof(buffer), "%02X", val);
	USART1_SendString(buffer);
}                                                         
/***********************************************************
Function:  Displays integers up to 127 as a hex value.
***********************************************************/
void USART1_Tx_int_8_hex( int8_t val )
{
	char buffer[5];
	snprintf(buffer, sizeof(buffer), "%02X", val);
	USART1_SendString(buffer);
}
/***********************************************************
Function:  Displays 16 bit integers as a hex value.
***********************************************************/
void USART1_Tx_UI_16_hex( uint16_t val )
{
	char buffer[7];
	snprintf(buffer, sizeof(buffer), "%04X", val);
	USART1_SendString(buffer);
}
/***********************************************************
Function:  Displays 32 bit integers as a hex value.
***********************************************************/
void USART1_Tx_UI_32_hex( uint32_t val )
{
	char buffer[11];
	snprintf(buffer, sizeof(buffer), "%08lX", val);
	USART1_SendString(buffer);
}
/***********************************************************
Function:  Displays 16 bit integers as a binary value.
***********************************************************/
void USART1_Tx_UI_8_bin( uint8_t val )
{
	char strVal[9];
	itoa(val, strVal, 2);

	char buffer[9];
    memset(buffer, '0', sizeof(buffer));
    strcpy(buffer + (sizeof(buffer) - strlen(strVal) - 1), strVal);

	USART1_SendString(buffer);
}
/***********************************************************
Function:  Displays 16 bit integers as a binary value.
***********************************************************/
void USART1_Tx_UI_16_bin( uint16_t val )
{
	char strVal[17];
	itoa(val, strVal, 2);

	char buffer[17];
    memset(buffer, '0', sizeof(buffer));
    strcpy(buffer + (sizeof(buffer) - strlen(strVal) - 1), strVal);

	USART1_SendString(buffer);
}

/************************************************************
  Function   : Check4string ()
  Purpose    : Searches for the string argument in RxBuffer.
  Parameters : strng: - string to search for.
				 : message: - return string .
  Returns    : NULL for fail
				  Ptr to first character.
  Notes      : strstr - returns a pointer pointing to the 
					first character of the found s2 in s1 otherwise
					a null pointer if s2 is not present in s1. 
					If s2 points to an empty string, s1 is returned. 
					AT+CGNSINF
Note: Check4string(char strng[]) changed to Check4string(char *strng)
**************************************************************************/
char * Check4string(char *strng)
{
	char *strtofstr = NULL;
		
	//TODO - Loop through char by char, else volatile is discarded
	strtofstr = strstr((const char *)UART_RxBuf, strng);

	if (strtofstr == NULL)
	 {
		 #ifdef _DEBUG_
		  USART1_SendString("\n\rNot Found...: \"");
		  USART1_SendString(strng) ;			  
		  USART1_SendString("\"");
		  #endif
	 }	
	 return (strtofstr);
}
/************************************************************
  Function   : Check4strArray ()
  Purpose    : Searches for the string argument in RxBuffer.
  Parameters : strng: - string to search for.
			 : message: - return string .
  Returns    : NULL for fail
				  Ptr to first character.
  Notes      : strstr - returns a pointer pointing to the
					first character of the found s2 in s1 otherwise
					a null pointer if s2 is not present in s1.
					If s2 points to an empty string, s1 is returned.
					AT+CGNSINF
Note: Check4string(char strng[]) changed to Check4string(char *strng)
**************************************************************************/
char * Check4strArray(char Rxbuf[], char *strng)
{
	char *strtofstr ;

	 strtofstr = strstr(Rxbuf, strng);

	if (strtofstr == 0)
	 {
		 #ifdef _DEBUG_
		  USART1_SendString("\n\rNot Found...: \"");
		  USART1_SendString(strng) ;
		  USART1_SendString("\"");
		  #endif
	 }
	 return (strtofstr);
}

/*------------------------------------------------------------------------------
  Function   : ISR(UART0_RECEIVE_INTERRUPT)
  Purpose    : Facilitates interrupt driven USART receive
  Parameters : None
  Returns    : Nothing
  Notes      : USART receive interrupt handler.  Interrupt handler gets
					called once data is received from the host
------------------------------------------------------------------------------*/
ISR(USART0_RX_vect)
{
	unsigned char cSREG = SREG;	

	UART_RxBuf[UART_RxPtr++] = UDR0;	
	if (UART_RxPtr >= UART_RX_BUFFER_SIZE)	
	{
		UART_RxPtr = 0;
	}

	intrflags.rx_intr = 1;
	
	SREG = cSREG;
	asm("sei");
}

/***********************************************************
After sending an AT command - waits for "OK"
************************************************************/
UI_8 Wait_for_OK(void)
{
   UI_8 index = 0  ;
   UI_8 rxiptr = 0;

	Timer1_init(4) ;				/* Start timeout for Rx of UART "OK"*/
	asm("sei");

	while(1)
	{
		if (intrflags.rx_intr == 1)
		{
			rxiptr = UART_RxPtr - 1 ;
			
			if (rxiptr > 0){index = rxiptr-1;} else {index = 0;}
			if (UART_RxBuf[index] == 'O' && UART_RxBuf[rxiptr] == 'K' )
			{
			Timer1_disable() ;				/* Disable Timer1 Interrupt*/
			return (SYS_PASS);
			}
			else if (UART_RxBuf[index] == 'E' && UART_RxBuf[rxiptr] == 'R' )
			{
			USART1_SendString("\r\nMODEM error!\r\n");
			Timer1_disable() ;				/* Disable Timer1 Interrupt*/
			return (SYS_FAIL);
			}
			intrflags.rx_intr = 0;
		}
		else if (intrflags.timer1_intr == 1)
		{
			Timer1_disable() ;				/* Disable Timer1 Interrupt*/
			intrflags.rx_intr = 0;
			intrflags.timer1_intr = 0 ;
			return (SYS_FAIL);
		}
	}
}

 /***********************************************************
-------- Search for a string in the RxBuffer: ===============
1. Take last char of string to search for and compare to current
	RxBuffer pointer.
2. If compare fails, wait for next Rx INTR an compare to next
	RxBuffer pointer value.
3. If compare is successful, wait for next Rx INTR an compare 
	the one lower compare string char to next RxBuffer pointer value.
4.	Repeat until the string length is reached. 
 ************************************************************/
UI_8 Wait_for_char(char ch)
{
	UI_8 index = 0  ;
	UI_8 rxiptr = 0;
	UI_8 debug = 0 ;
	//char *strptr = NULL ;

	Timer1_init(4) ;				/* Start timeout for Rx of UART "OK"*/
	asm("sei");

	do
	{
		if (intrflags.rx_intr == 1)
		{
			rxiptr = UART_RxPtr - 1 ;
			
			if (rxiptr > 0){index = rxiptr-1;} else {index = 0;}
			if (UART_RxBuf[index] == ch)
			{				
				if (debug)
				{
					lcd_Clear() ;
					//lcd_SetCursorPos(0, 0);
					//lcd_Txt ("Received >", true);
					lcd_display_string("Received >", 0, LCD_LINE_1);
				}
				Timer1_disable() ;				/* Disable Timer1 Interrupt*/
				return (SYS_PASS);
			}			
			intrflags.rx_intr = 0;
		}
		else if (intrflags.timer1_intr == 1)
		{
			if (0)
			{
				//lcd_Clear() ;
				//strptr = "> Timed out!" ;
				//lcd_display_string(strptr, 0, LCD_LINE_1) ;
				USART1_SendString("\n\r> Timed out!");
			}
			if (debug)
			{
				//lcd_display_itoa ("tO-1:", intrflags.timer1_intr, 0, LCD_LINE_4, (11));
				//delay_ms_1(1000);
				//lcd_display_char("RxB:", UART_RxBuf[rxiptr], 0, LCD_LINE_2, 6) ;
				//lcd_display_char("RxB:", UDR0, 0, LCD_LINE_3, 6) ;
				//delay_ms_1(1000);
			}
			
			Timer1_disable() ;				/* Disable Timer1 Interrupt*/
			intrflags.rx_intr = 0;
			intrflags.timer1_intr = 0 ;
			return (SYS_FAIL);
		}
	} while (1);
}

void USART1_ClearBuffer(void)
{
	UART_RxPtr = 0 ;	/* Zero ptr to fill UART Rx buffer from start*/
}

/*-----------------------------------------------------------
  Function   : Wait4RxBuf2Fill()
  Purpose    : Fills buffer with received amount of UART data
  Parameters : cnt - nr of bytes to capture in buffer
  Returns    : Pass or fail.
  Notes      : UART Rx must enabled prior to this function.
------------------------------------------------------------*/
UI_8 USART1_Wait4RxBuf2Fill (UI_8 cnt, UI_8 time_out)
{
	UI_8 repeatcnt = 0 ;

	Timer1_init(4) ;				/* Start timeout for Rx of UART "OK"*/	
	asm("sei");

	//UART_RxPtr = 0 ;	
	USART1_ClearBuffer();
	while (1)
	{
		while (intrflags.timer1_intr != 1)
		{
			if (intrflags.rx_intr == 1)
			{
				if (UART_RxPtr >= cnt)	/* UART Ptr is inc in the UART Rx Routine */
				{
					Timer1_disable() ;				/* Disable Timer1 Interrupt*/
					intrflags.rx_intr = 0;
					intrflags.timer1_intr = 0 ;
					return (SYS_PASS);
					//break ;
				}						
				intrflags.rx_intr = 0;
			}
		} 	
		if (intrflags.timer1_intr == 1)	/* 4 sec timeout */
		{
			if (repeatcnt > time_out)		/* 18 x 4s=just over 1min*/
			{
				USART1_SendString("\n\rTIME=OUT");
				Timer1_disable() ;				/* Disable Timer1 Interrupt*/
				intrflags.rx_intr = 0;
				intrflags.timer1_intr = 0 ;
				return (SYS_FAIL) ;
			}			
			repeatcnt++ ;				
			intrflags.timer1_intr = 0 ;
		}
	}	
}

UI_8 USART1_GetBufferSize(void)
{
	return UART_RxPtr;
}

void USART0_PackData(void)
{
	UI_8 data;
	UI_8 size;
	unsigned char *buffer;

	// There is nothing in the buffer
	if (UART_RxPtr == 0)
		return;

	USART1_SendString("Packing %d bytes data:\r\n", UART_RxPtr);

    // Copy data while interrupts are disabled, then re-enable them
	asm("cli");
	size = UART_RxPtr;
	buffer = malloc(size);
	memcpy(buffer, (const void *)UART_RxBuf, size);
	USART1_ClearBuffer();
	asm("sei");

	//HexDump("DATA:", buffer, size);

	for (int i = 0; i < size; i++)
	{
		data = buffer[i];
		LineBuffer[WriteLine][WriteChar] = data;

		// Ignore newlines ...
		 if (data == '\n')
			continue;

		if (data == '\r')
		{
			// Don't add the CR character to the buffer
			LineBuffer[WriteLine][WriteChar] = '\0';

			// Start a new line
			WriteLine++;
			if (WriteLine == MAX_LINES)
				WriteLine = 0;

			WriteChar = 0;
		}
		else
		{
			WriteChar++;
			if (WriteChar == MAX_CHARS)
			{
				// Line buffer overrun
				WriteChar = 0;	
			}
		}
	}
	free(buffer);

	USART0_HexDumpBuffer();
}

void USART0_HexDumpBuffer(void)
{
	int l;
	char str[24];
	
	console_write("Write (%02d,%02d) ", WriteLine, WriteChar);
	console_writeline("Read (%02d,%02d)\r\n", ReadLine, 0);

    l = ReadLine;
	while (l != WriteLine)
	{
		if ((l != WriteLine) || (WriteChar > 0))
		{
			snprintf(str, sizeof(str), "Line %02d: ", l);
			hex_dump(str, LineBuffer[l], strlen(LineBuffer[l]));
		}

		if (++l == MAX_LINES)
			l = 0;
	}

	console_writeline("---");
}

char *USART0_GetNextLine(void)
{
	USART0_PackData();

	// No new lines to read
	if (ReadLine == WriteLine)
		return NULL;

	// Return a pointer to the line to avoid excessive memcpy operations
	return &LineBuffer[ReadLine][0];
}

void USART0_MoveNextLine()
{
	// Only move of there is unread data in the buffer
	if (ReadLine != WriteLine)
	{
		ReadLine++;
		if (ReadLine == MAX_LINES)
			ReadLine = 0;
	}
}

UI_8 USART0_CompareLines(char *buffer, char *expected)
{
	UI_8 i = 0;
	UI_8 e = 0;

	while (i < strlen(buffer))
	{
		if (buffer[i++] == expected[e])
		{
			e++;

			if (e == strlen(expected))
			{
				USART1_SendString("Found %s at (%02d,%02d)\r\n", expected, ReadLine, i);
				return true;
			}
		}
		else
		{
			e = 0;
		}
	}

	return false;
}

UI_8 USART0_WaitForStringWithData(char *expected, char *data, UI_8 maxDataSize)
{
	char *lineBuffer;
	UI_8 dataSize = 0;
	UI_16 delay = 100;
	UI_16 max_retries = 60;
	UI_16 retry = 0;

	while (retry++ < max_retries)
	{
		// Give some time for the buffer to fill up
		delay_ms_1(delay);

		if ((lineBuffer = USART0_GetNextLine()) == NULL)
		{
			USART1_SendChar('.');
			delay_ms_1(delay);
			continue;
		}

		if (USART0_CompareLines(lineBuffer, expected))
		{
			USART0_MoveNextLine();
			return SYS_PASS;
		}
		else
		{
			if (USART0_CompareLines(lineBuffer, "+CME ERROR"))
				return SYS_FAIL;

			// Copy the data
			if (dataSize < maxDataSize)
			{
				UI_8 remaining = maxDataSize - dataSize;
				if (strlen(lineBuffer) <= remaining)
					remaining = strlen(lineBuffer);

				memcpy(&data[dataSize], lineBuffer, remaining);
				dataSize += remaining;
			}
		}

		USART0_MoveNextLine();
	}

	USART1_SendString("WaitForString failed - Max retries reached!\r\n");
	return SYS_FAIL;

}

UI_8 USART0_WaitForString(char *expected)
{
	return USART0_WaitForStringWithData(expected, NULL, 0);
}

UI_8 USART0_SendCommand(char *command, char *expected)
{
	USART1_SendString("CMD: [%s] [%s]\r\n", command, expected);

	// Get all remaining data from the modem
	// USART0_PackData();

    // Only write a command if there is one
    if (strlen(command) > 0)
	{
		USART0_SendString(command);
		USART0_SendString("\r");
		
		// Wait for the modem to echo the command back
		if (USART0_WaitForString(command) == SYS_FAIL)
		{
			USART1_SendString("Send command failed.\r\n");
			return SYS_FAIL;
		}
	}

	if (strlen(expected) == 0)
		return SYS_PASS;

	if (USART0_WaitForString(expected) == SYS_FAIL)
	{
		USART1_SendString("Expected result failed.\r\n");
		return SYS_FAIL;
	}

	return SYS_PASS;
}

UI_8 USART0_ReadData(char *command, char *expected, char *data, UI_8 maxDataSize)
{
	USART1_SendString("DATA: [%s] [%s]\r\n", command, expected);

	// Get all remaining data from the modem
	// USART0_PackData();

    // Only write a command if there is one
    if (strlen(command) > 0)
	{
		USART0_SendString(command);
		USART0_SendString("\r");
		
		// Wait for the modem to echo the command back
		if (USART0_WaitForString(command) == SYS_FAIL)
		{
			USART1_SendString("Send command failed.\r\n");
			return SYS_FAIL;
		}
	}

	if (strlen(expected) == 0)
		return SYS_PASS;

	if (USART0_WaitForStringWithData(expected, data, maxDataSize) == SYS_FAIL)
	{
		USART1_SendString("Expected result failed.\r\n");
		return SYS_FAIL;
	}

	return SYS_PASS;
}

//TODO - Die parsing van die buffer suck, maar sscanf wil nie werk nie...
UI_8 USART0_ParseDataBalance(void)
{
	char *c1;
	char *c2;
	char *lineBuffer;
	UI_16 delay = 100;
	UI_16 max_retries = 600;
	UI_16 retry = 0;


	while (retry++ < max_retries)
	{
		// Give some time for the buffer to fill up
		delay_ms_1(delay);

		if ((lineBuffer = USART0_GetNextLine()) == NULL)
		{
			USART1_SendChar('.');
			delay_ms_1(delay);
			continue;
		}
		USART1_SendString("\r\n");
		USART0_MoveNextLine();

		if ((c1 = strstr(lineBuffer, "Data:")) != NULL)
		{
			if ((c2 = strstr(c1, "Available")) != NULL)
			*c2 = '\0';
			USART1_SendString("\t\t*** Available data: %s ***\r\n", c1+5);
			return SYS_PASS;
		}
	}

	return SYS_FAIL;
}

UI_8 USART0_GetUnsolicitedData(char *buffer, UI_16 maxSize)
{
	char *lineBuffer = NULL;
	UI_16 remaining;

	while (1)
	{
		if ((lineBuffer = USART0_GetNextLine()) == NULL)
			return 0;

		if (lineBuffer[0] != '+')
		{
			USART0_MoveNextLine();
			continue;
		}

		break;
	}

	remaining = strlen(lineBuffer);
	if (remaining >= maxSize-1)
		remaining = maxSize-1; 
	memcpy(buffer, lineBuffer, remaining);
	buffer[remaining] = '\0';
	USART0_MoveNextLine();

	return remaining;
}