/*
 * eeprom.c
 *
 * Created: 2019/05/22 06:54:39 PM
 *  Author: Bronko
 */ 
#include <stdio.h>
#include <avr/io.h>
#include <avr/eeprom.h>
#include "types.h"
#include "system.h"
#include "usart.h"

UI_8 eeprom_updated = 0 ;

void EEPROM_WR_byte(UI_16 addr, UI_8 data) ;
UI_8 EEPROM_RD_byte(UI_16 addr) ;
char EEPROM_RD_Char(UI_16 addr) ;
void EEPROM_WR_string(UI_16 addr, char *s, UI_8 lngt) ;
void EEPROM_RD_string(UI_16 addr, char *s, uint8_t len) ;

void EEPROM_WR_byte(UI_16 addr, UI_8 data)
{
  //UI_8 retval = 0xFFFFF  ;

  while(EECR & (1<<EEPE)) ; /* wait until previous write any */
  
	EEAR = addr;
	EEDR = data;
	EECR |= (1<<EEMPE);
	EECR |= (1<<EEPE);

	//retval = EEPROM_RD_byte(addr) ;
	//if (retval == data)
		//console_writeline("\n\r\tEEPROM WR PASS!");		
	//else
		//console_writeline("\n\r\tEEPROM WR FAIL!");	
}

UI_8 EEPROM_RD_byte(UI_16 addr)
{
	while(EECR & (1<<EEPE)) ;/*wait until previous write any*/
	  
	EEAR = addr;
	EECR |= (1<<EERE);
	return EEDR;
}

char EEPROM_RD_Char(UI_16 addr)
{
	while(EECR & (1<<EEPE)) ;/*wait until previous write any*/
	
	EEAR = addr;
	EECR |= (1<<EERE);
	return EEDR;
}

/*assumes s is a proper null terminated string*/
void EEPROM_WR_string(UI_16 addr, char *s, UI_8 lngt)
{
	UI_8 i = 0 ;

	for (i = 0; i <= lngt; i++)
	{
	  EEPROM_WR_byte(addr, *s );
	  ++s;
	  ++addr;
	}
}

/* read a string of len characters maximum starting at addr. 
 * modify according to your need!
 */
void EEPROM_RD_string(UI_16 addr, char *s, uint8_t len)
{
	while(len)
	{
	  *s = EEPROM_RD_byte(addr++);
	  
	  // Nothing written at that address
	  if (*s == 0xFF)
	  	break;

	  // End of string
	  if(*s == '\0')
		  break;

	  len--;
	  s++;
	}

	*s = '\0';
}