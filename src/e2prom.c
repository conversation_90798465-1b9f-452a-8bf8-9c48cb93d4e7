/*
 * eeprom.c
 *
 * Created: 2019/05/22 06:54:39 PM
 *  Author: Bronko
 */ 
#include <stdio.h>
#include <avr/io.h>
#include <avr/eeprom.h>
#include "types.h"
#include "system.h"
#include "usart.h"

uint8_t eeprom_updated = 0 ;

void EEPROM_WR_byte(uint16_t addr, uint8_t data)
{
  //uint8_t retval = 0xFFFFF  ;

  while(EECR & (1<<EEPE)) ; /* wait until previous write any */
  
	EEAR = addr;
	EEDR = data;
	EECR |= (1<<EEMPE);
	EECR |= (1<<EEPE);

	//retval = EEPROM_RD_byte(addr) ;
	//if (retval == data)
		//console_writeline("\n\r\tEEPROM WR PASS!");		
	//else
		//console_writeline("\n\r\tEEPROM WR FAIL!");	
}

uint8_t EEPROM_RD_byte(uint16_t addr)
{
	while(EECR & (1<<EEPE)) ;/*wait until previous write any*/
	  
	EEAR = addr;
	EECR |= (1<<EERE);
	return EEDR;
}

char EEPROM_RD_Char(uint16_t addr)
{
	while(EECR & (1<<EEPE)) ;/*wait until previous write any*/
	
	EEAR = addr;
	EECR |= (1<<EERE);
	return EEDR;
}

/*assumes s is a proper null terminated string*/
void EEPROM_WR_string(uint16_t addr, char *s, uint8_t lngt)
{
	uint8_t i = 0 ;

	for (i = 0; i <= lngt; i++)
	{
	  EEPROM_WR_byte(addr, *s );
	  ++s;
	  ++addr;
	}
}

/* read a string of len characters maximum starting at addr. 
 * modify according to your need!
 */
bool EEPROM_RD_string(uint16_t addr, char *s, uint8_t len)
{
	while(len)
	{
	  *s = EEPROM_RD_byte(addr++);

	  // Nothing written at that address
	  if (*s == 0xFFFF)
	  	return false;

	  // End of string
	  if(*s == '\0')
		  break;

	  len--;
	  s++;
	}

	*s = '\0';
	return true;
}