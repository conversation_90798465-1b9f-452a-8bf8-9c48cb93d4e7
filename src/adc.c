/*
 * adc.c
 *
 * Created: 2018/07/08 08:18:30 PM
 *  Author: Bronko
 */ 
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include "types.h"
#include "delay.h"
#include "mqtt.h"
#include "console.h"

/* ADC Configuration */
#define ADC_NUM_SAMPLES 30

/* Global variables for ADC */
volatile uint8_t ADC_conversion_complete = 0;
volatile uint16_t ADC_result = 0;

/************************************************************************
 * ADC Interrupt Service Routine
 ************************************************************************/
ISR(ADC_vect) 
{ 
    ADC_result = ADCW;  // Use ADCW macro for 16-bit result
    ADC_conversion_complete = 1;
}

/************************************************************************
 * Initialize ADC for specified channel
 ************************************************************************/
static void adc_init_channel(uint8_t channel)
{
    // Enable ADC clock
    PRR0 &= ~(1 << PRADC);
    
    // Clear ADMUX and set reference to 2.56V
    ADMUX = 0x00;
    // Use 2.56V reference like working TMS 3.0
    ADMUX |= (1 << REFS0) | (1 << REFS1); // Set ADC reference to 2V56
    ADMUX &= ~(1 << ADLAR); // right adjust ADC result - 10 bit reading
    
    // Clear channel bits and set new channel
    ADMUX &= 0xF0;  // Clear MUX3:0 bits (keep reference and ADLAR settings)
    ADMUX |= (channel & 0x0F);  // Set channel bits directly
    
    ADCSRA |= (1 << ADEN);  /* Enable ADC */
    ADCSRA |= (1 << ADIE);  /* Enable ADC Interrupt */
    /* Set ADC prescalar to 128 - 125KHz sample rate @ 16MHz */
    ADCSRA |= (1 << ADPS0) | (1 << ADPS1) | (1 << ADPS2);
}

/************************************************************************
 * Perform single ADC conversion
 ************************************************************************/
static uint16_t adc_single_conversion(void)
{
    uint16_t timeout = 0;
    
    // Clear completion flag and start conversion
    ADC_conversion_complete = 0;
    ADCSRA |= (1 << ADSC);
    
    // Wait for conversion to complete with timeout
    while (!ADC_conversion_complete && timeout < 10000) {
        delay_ms(1);
        timeout++;
    }
    
    if (timeout >= 10000) {
        console_writeline("ADC timeout");
        return 0;
    }
    
    return ADC_result;
}

/************************************************************************
 * Main ADC measurement function - called externally
 ************************************************************************/
uint16_t adc_meas(uint8_t adc_channel)
{
    uint32_t sum = 0;
    uint16_t reading = 0;
    uint8_t i;

    // Initialize ADC for this channel
    adc_init_channel(adc_channel);
    
    console_writeline("adc_meas: Channel %d readings:", adc_channel);
    
    // Take samples for averaging
    for (i = 0; i < ADC_NUM_SAMPLES; i++) {
        reading = adc_single_conversion();
        // console_writeline("Sample %d: %d", i, reading);
        
        sum += reading;
        
        delay_ms(1); // Small delay between samples
    }
    
    // Calculate average
    reading = sum / ADC_NUM_SAMPLES;
    console_writeline("Average ADC reading:  %d", reading);
    
    // Back to 2.56V reference like TMS 3.0, use exact same scaling
    reading = (reading * 10) / 72;
    console_writeline("Scaled result: %d (displayed as %d.%d V)", reading, reading/10, reading%10);
    
    return reading;
}