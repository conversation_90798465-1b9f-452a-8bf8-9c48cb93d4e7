/*
 * io_pins.c
 *
 * Created: 2019/05/28 08:04:22 PM
 *  Author: <PERSON><PERSON><PERSON>
 *
 * Writing a 1 in the pin position in the DDRx will configure that pin as an output pin 
 * DDRB |= 1<<5; // Configuring PB5 as Output
 * PORTB |= 1<<5; // Writing HIGH to PB5
 *
 */ 
 #include <avr/io.h>
 //#include <string.h>
 //#include <stdlib.h> 
 //#include "lcd.h"
 //#include <inttypes.h>
#include "types.h"
#include "system.h"
#include "console.h"
//#include "delay.h"
#include <util/delay.h>

 uint8_t PinRD(uint8_t pin, uint8_t PinRegister) ;
 void PinWR(UI_8 pin, volatile UI_8 *Port, UI_8 value);
 /******************************************************************
  Function   : <PERSON>n<PERSON>
  Purpose    : Write the level of "value" the pin of the Port.
  Parameters : IO - Pin, Port, Level value
  Returns    : Nothing
  Notes      : e.g. PinRD(PIND5, PIND) ; 
******************************************************************/ 
 uint8_t PinRD(uint8_t pin, uint8_t PinRegister)
 {
	 UI_8 temp = 0 ;

	 if (PinRegister == IO_PORTA)
	 {
		 DDRA &= ~(1<<pin)  ; // Configuring as input pin.
		 PinRegister = PINA ; 
	 }
	 else if (PinRegister == IO_PORTB)
	 {
		 if (pin != PINB3)		/* Trap for BATTERY CUTOFF MOSFET !!!!!!!!!*/
		 {
			 DDRB &= ~(1<<pin)  ; // Configuring pin as input
		 }
		 else if (pin == PINB3)
		 {
			 console_writeline("\n\r\t\t\tStruck PINB3 RD!!!");
		 }
		 PinRegister = PINB ;
	 }
	 else if (PinRegister == IO_PORTC)
	 {
		 DDRC &= ~(1<<pin)  ; // Configuring as input
		 PinRegister = PINC ;
	 }
	 else if (PinRegister == IO_PORTD)
	 {
		 DDRD &= ~(1<<pin)  ; // Configuring as input
		 PinRegister = PIND ;
	 }

	 asm("nop") ;		// For synchroniser
	 
	 temp = PinRegister ;
	 temp = temp >> pin; // Shift the register with position to right
	 temp = temp & 0x01; //Mask MSB out
	 //lcd_display_itoa ("Reg:", PinRegister , 0, LCD_LINE_4, (8));
	 //delay_ms_1(4000) ;
	 if (temp)
	 {
		 return 1;
	 }
	 else return 0;
 }



/******************************************************************
  Function   : PinWR
  Purpose    : Write the level of "value" the pin of the Port.
  Parameters : IO - Pin, Port, Level value
  Returns    : Nothing
  Notes      :
******************************************************************/
void PinWR(UI_8 pin, volatile UI_8 *Port, UI_8 value)
{
	if (*Port == PORTA)
	{
		DDRA |= 1 << pin ;   // Direction of pin PA set as Output	
	}
	else if (*Port == PORTB)
	{
		DDRB |= 1 << pin ;   // Direction of pin PB set as Output
	}
	else if(*Port == PORTC)
	{
		////lcd_display_string("Port C:", 0, LCD_LINE_2);
		DDRC |= 1 << pin ;   // Direction of pin PC set as Output
	}
	else if(*Port == PORTD)
	{
		////lcd_display_string("Port D:", 0, LCD_LINE_3);
		DDRD |= 1 << pin ;   // Direction of pin PD set as Output
	}
	
	if (value == 0)
	{
			*Port &= ~(1 << pin);   // clears bit
	}
	else
	{
		*Port |= (1 << pin);   // set bit		
	}	
}



#if 0
//How to pass an IO port as a parameter to a function:
#include <inttypes.h>
#include <avr/io.h>

void set_bits_func_correct (volatile uint8_t *port, uint8_t mask)
{
	*port |= mask;
}

#define set_bits_macro(port,mask) ((port) |= (mask))

int8_t main (void)
{
	set_bits_func_correct (&PORTB, 0x55);
	set_bits_macro (PORTB, 0xf0);
	return (0);
}
#endif