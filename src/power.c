#include "adc.h"
#include "console.h"

#include "power.h"

static Power s_power = {0, 0};

const Power *power_get(void)
{
    s_power.battery = ADC_Meas(POWER_BATTERY);
    s_power.mains = ADC_Meas(POWER_MAINS);

    return &s_power;
}

const char *power_get_mains_string(void)
{
    static char power_string[8];

    snprintf(power_string, sizeof(power_string), "%d.%d",
             s_power.mains / 10,
             s_power.mains % 10);

    return power_string;
}

const char *power_get_battery_string(void)
{
    static char power_string[8];

    snprintf(power_string, sizeof(power_string), "%d.%d",
             s_power.battery / 10,
             s_power.battery % 10);

    return power_string;
}