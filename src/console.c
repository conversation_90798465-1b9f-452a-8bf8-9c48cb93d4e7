#include "usart.h"

#include "console.h"

void console_init(void)
{
    USART1_Init(BR115200);
}

void console_write(const char *format, ...)
{
    char buffer[256];
    va_list args;
    va_start(args, format);
    vsnprintf(buffer, sizeof(buffer), format, args);
    va_end(args);

    USART1_SendRawString(buffer);
}

void console_writeline(const char *format, ...)
{
    char buffer[256];
    va_list args;
    va_start(args, format);
    vsnprintf(buffer, sizeof(buffer), format, args);
    va_end(args);

    USART1_SendRawString(buffer);
    USART1_SendRawString("\r\n");
}