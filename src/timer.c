/*
 * Timer.c
 *
 * Created: 2018/08/08 03:29:02 PM
 *  Author: <PERSON><PERSON><PERSON>
 */ 
  
 #include <avr/io.h>
 #include <avr/interrupt.h>
 #include <stdlib.h>

#include "types.h"
#include "system.h"
#include "lcd.h"
#include "delay.h"
#include "usart.h"
#include "modem.h"
#include "interrupts.h"
#include "io_pins.h"
#include "console.h"

/* For 11.0593MHz
------------------*/
#define t_025s 0xF573
#define t_05s 0xEAE7 
#define t_1s 0xD5CF 
#define t_2s 0xAB9F
#define t_3s 0x816F
#define t_4s 0x573F


/*-----------------------------------------
  FUNCTIONS PROTOTYPES
-------------------------------------------*/
void Timer1_init(UI_8 sec);
void Timer1_disable(void);
void Timer_set_Norm_Mod(void) ;

/*-------------------------------------------
 GLOBAL VARIABLES:
						 
Note:	
Global variables cannot be initialized because 
they only serve as a storage class.
-------------------------------------------*/
//volatile uint8_t tot_compA_cnt ;
volatile uint8_t time_Hour ;
volatile uint8_t time_Min ;
volatile uint8_t time_Sec ;
volatile UI_16 n_prev = 0;

extern volatile UI_16 tick_SEC ;

/************************************************************************************
 Timer_set_Norm_Mode():
 ---------------------
 Purpose:	Sets up timer1 for nr of seconds  @ 16Mhz Clock (mode 4)
			Enables the Compare Interrupt.
 Parameters: None
 Returns:	None

 OCR1A: = (fclk/2 * Prescaler) * time 
 16Mhz/1024 = 15625 * 3.5s = 0xD59F
 e.g.  OCR1A = 0x3D08 -> Output Compare Register value for 1s.
 ************************************************************************************/
 void Timer_set_Norm_Mod(void)
 {
	unsigned char cSREG;
		
	cSREG = SREG;			/* store SREG value */	
	asm("cli");
	
	 /* Normal mode: 
	 --------------- */
	 TCCR1A = 0 ; 
	 TCCR1B = 0 ;					// Mode 0, Free running
	 OCR1A = 0xFFFF;

	 TCCR1B |= (1 << CS12) | (1 << CS10);	// set prescaler to 1024 and start the timer	 

	 SREG = cSREG;
	 asm("sei");
	 
 }
 /************************************************************************************
 Timer1_Init():
 --------------
 Purpose:	Sets up timer1 for nr of seconds  @ 16Mhz Clock (mode 4)
			Enables the Compare Interrupt.
 Parameters: None
 Returns:	None

 OCR1A: = (fclk/2 * Prescaler) * time 
 16Mhz/1024 = 15625 * 3.5s = 0xD59F
 e.g.  OCR1A = 0x3D08 -> Output Compare Register value for 1s.

 NB, NOTE:  The timer time-out is extended through only setting the 
 "intrflags.timer1_intr = 1" after X.. nr of timer interrupts has occurred.
 This happens in the TIMER1_COMPA_vect ISR.
 ************************************************************************************/
 void Timer1_init(UI_8 sec)
 {
	unsigned char cSREG;
		
	cSREG = SREG;			/* store SREG value */	
	asm("cli");

	//tot_compA_cnt = 0  ;
	intrflags.timer1_intr = 0 ;

//if (sec == 1) {OCR1A = t_1s;}					// 1 sec
	//else if (sec == 2) {OCR1A = t_2s;}			// 2 sec
	//else if (sec == 3) {OCR1A = 0xD59F;}		// 3.5 sec
	//else if (sec == 4) {OCR1A = 0xFFFF;}		// 8.4 sec

	switch (sec)
	{
		case 1: 
			OCR1A = t_1s;
		break;
		case 2: 
			OCR1A = t_2s;
		break;
		case 3:
			OCR1A = 0xD59F;		// 3.5 sec
		break;
		case 4:
			OCR1A = 0xFFFF;		// 8.4 sec
		break;			
		default: 
			OCR1A = 0xFFFF;		// 8.4 sec
	}

	 /* Normal mode: 
	 --------------- */
	 TCCR1A = 0 ; 
	 TCCR1B |= (1 << WGM12);					// Mode 4, CTC on OCR1A	

	 TCCR1B |= (1 << CS12) | (1 << CS10);	// set prescaler to 1024 and start the timer	 

	 TIMSK1 |= (1 << OCIE1A) ;					// Set interrupt on compare match	

	 SREG = cSREG;
	 asm("sei");	 
 }
 
/************************************************************************************
 Timer Interrupt Service Routine():
 -----------------------------------
 Purpose: Interrupt Service Routine for Timer1 Compare Vector
 Parameters: None
 Returns: None
 Note: OCFA is automatically cleared when the Output Compare Match A 
 Interrupt Vector is executed.

 * Using "tot_compA_cnt" the Timer1 time can be extended.
 ************************************************************************************/
 ISR (TIMER1_COMPA_vect)
 {
	unsigned char cSREG;
	static uint8_t tot_compA_cnt ;		 

	asm("cli");
	cSREG = SREG;			/* store SREG value */
	
	tot_compA_cnt ++ ;

	if (tot_compA_cnt > 2)
	{	 
		intrflags.timer1_intr = 1 ;		
		tot_compA_cnt = 0  ;
	}	
	// console_writeline("\n\rTimer1 COMPA\n\r");

	SREG = cSREG;			/* restore SREG value (I-bit) */
	asm("sei");
 }

/************************************************************************************
 Timer_disable():
 -----------------------------------
 Purpose: Stops timer
 Parameters: None
 Returns: None
 ************************************************************************************/
void Timer1_disable(void)
{
	unsigned char sreg;	

	sreg = SREG;
	asm("cli");

	intrflags.timer1_intr = 0 ;
	//tot_compA_cnt = 0  ;

	TIMSK1 &= ~(1 << OCIE1A);		/* Disable interrupt on compare match */

	TCCR1B = 0 ;	/* Stop the clock */
	TCNT1	 = 0 ;	/* Reset Counter  */

	SREG = sreg;
}

 /************************************************************************************
Timer2_RTC_init():
 ------------------
 Purpose: Sets up timer2 for 1 second @32.768kHz Clock (normal mode).
 Parameters: None
 Returns:	None

 ************************************************************************************/
 void Timer2_RTC_init(void)
 {
	unsigned char cSREG;
		
	cSREG = SREG;			/* store SREG value */	
	asm("cli");

	  /* TC2 is enabled when (PRR0.PRTIM2) = '1' 
  ------------------------------------------*/
	PRR0 |= (1 << PRTIM2) ;		
	
	TCCR2A = 0 ;		/* Set for Normal mode */	
	TCCR2B = 0 ;

	/* Timer/Counter2 is clocked from a crystal connected 
		to the Timer Oscillator 1 (TOSC1) pin. 
	----------------------------------------------------*/
	ASSR = 0 ;
	//ASSR |= (1 << AS2) | (1 << EXCLK) ;		

	/* The prescaler is reset by writing a '1' 
	Allows presecaler to be written.
	------------------------------------------*/
	GTCCR |= (1 << PSRASY)  ; 

	/* Normal mode: Set prescaler to 256 with 256 run over = 1s
	----------------------------------------------------------- */	 
	TCCR2B |=  (1 << CS22) | (1 << CS21) ;

	/* Normal mode: Set Output Compare Register to 255 with 
	   256 run over = 1s for Output Compare Interrupt.
	----------------------------------------------------------- */
	OCR2A = 0xFF;
	OCR2B = 0xFF;			

	/* Timer/Counter2 Overflow interrupt is enabled 
	----------------------------------------------*/
	TIMSK2 |= (1 << TOIE2) ;		
				

	 SREG = cSREG;
	 asm("sei");	 
 }
 /************************************************************************************
 Timer2 Overflow Interrupt Service Routine():
 -------------------------------------------------
 Purpose: Interrupt Service Routine for 1 sec.
 Parameters: None
 Returns: None

 Note: TOV2 is automatically cleared when the Interrupt Vector is 
		 executed.
 ************************************************************************************/
 ISR (TIMER2_OVF_vect)
 {
	unsigned char cSREG;
	
	//static UI_8 bit ;
	//static UI_8 time_diff ;
	console_writeline("\n\rTOV2 Intr: ");
	//HALT		 

	cSREG = SREG;			/* store SREG value */
	asm("cli");
	
	//tick_SEC++ ; !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!

	/* TOV bit is set when an overflow occurs in Timer/Counter2.
	------------------------------------------------------------*/	
	TIFR2 &= ~(1 << TOV2) ;
		
	SREG = cSREG;			/* restore SREG value (I-bit) */
	asm("sei");
 }

/************************************************************************************
Timer2_COMPA_init():
 --------------
 Purpose:	Sets up timer1 for nr of seconds  @ 16Mhz Clock (mode 4)
			Enables the Compare Interrupt.
 Parameters: None
 Returns:	None

 OCR1A: = (fclk/2 * Prescaler) * time 
 11.0592Mhz/1024 = 10800 (0x2A30)
 1s: OCRA = 0x2A30
 ************************************************************************************/
 void Timer2_COMPA_init(void)
 {
	unsigned char cSREG;
		
	cSREG = SREG;			/* store SREG value */	
	asm("cli");

	OCR2A = 0xFF ;		// ?????????? sec

	 /* Timer Compare mode: 
	 ---------------------- */
	 TCCR2A = 0 ; 
	 TCCR2A |= (1 << WGM21);					// Mode 2, CTC on OCR2A	

	 TCCR2B |= (1 << CS22) | (1 << CS21) | (1 << CS20);	// set prescaler to 1024 and start the timer	 

	 TIMSK2 |= (1 << OCIE2A) ;					// Set interrupt on compare match	

	// GTCCR |= (1 << TSM) ;

	 SREG = cSREG;
	 asm("sei");
	 
 }
 /************************************************************************************
Timer/Counter2 Compare Match A Interrupt Service Routine():
 ---------------------------------------------------------
 Purpose: Interrupt Service Routine for 1 sec.
 Parameters: None
 Returns: None

 Note: TOV2 is automatically cleared when the Interrupt Vector is 
		 executed.
 ************************************************************************************/
 ISR (TIMER2_COMPA_vect)
 {
	unsigned char cSREG;
	
	//static UI_8 bit ;
	//static UI_8 time_diff ;
	//console_writeline("\n\rT2CMatchA Intr: ");
	//HALT		 

	cSREG = SREG;			/* store SREG value */
	asm("cli");
	
	// tick_SEC++ ;	!!!!!!!!!!!!!!!!!!!!!!!!!!!

	/* TOV bit is set when an overflow occurs in Timer/Counter2.
	------------------------------------------------------------*/	
	TIFR2 &= ~(1 << TOV2) ;

	USART1_Tx_UI_16_dec(tick_SEC) ;
	
	//if (time_Sec >= 60)
	//{
		//time_Sec += (time_Sec - 60)  ;
	//}
	//else if (time_Sec == 60) time_Sec = 0  ;
	
	//bit = ~bit ;
	//PinWR(GREEN_LED5 , &PORTA, bit) ;
		
	SREG = cSREG;			/* restore SREG value (I-bit) */
	asm("sei");
 }
 /*******************************************************************************************
 Timer3_Norm_Init():
 -----------------
 Purpose:	Sets up timer3 for free running mode @ 16Mhz Clock (mode 1)
			   
 Parameters: None
 Returns:	None

 TOV3: = 1/(fclk/(1)Prescaler) * 0xFFFF(65535) = 6.068s			
			[1/(11.0592Mhz/128)/65535(0xFFFF) = 1.318s]
1s:
	11.0592Mhz/1024 = 10800Hz(92.592 us)
	1s/92.592us = 10800
	65535(0xFFFF) - 10800 = 54735 (0xD5CF)
0.5s:
	0.5s/92.592us = 5400
	65535(0xFFFF) - 5400 = 60135 (0xEAE7)
 ************************************************************************************/
 void Timer3_Norm_init(void)
 {
	unsigned char cSREG;
		
	cSREG = SREG;			/* store SREG value */	
	asm("cli");	

	 /* Set Normal mode & prescaler to 1024.
	 ------------------------------------- */
	 TCCR3A = 0 ;				// Normal MODE, not for waveform generation.
	 TCCR3B |= (1 << CS32) | (1 << CS30) ;	 
	
	//TCNT3 =  t_1s ;
	//TCNT3 =  t_05s ;
	TCNT3 =  t_025s ;

	 /* Enable OCRA - OCF3 flag bit */
	// TIMSK3 |= (1 << OCIE3A) ;			// Set interrupt on compare match	
	TIMSK3 |= (1 << TOIE3) ;		// Set interrupt on overflow	


	 SREG = cSREG;
	 asm("sei");
	 
 }
 /************************************************************************************
 Timer3 Compare match Interrupt Service Routine():
 -------------------------------------------------
 Purpose: Interrupt Service Routine for Timer3 Compare Vector
			 Increments "time_Sec" value with 4 sec.
 Parameters: None
 Returns: None

 Note: TOV3 is automatically cleared when the Interrupt Vector is 
		 executed.
 ************************************************************************************/
 ISR (TIMER3_OVF_vect)
 {
	unsigned char cSREG;
	//static UI_8 bit ;
	//static UI_8 time_diff ;
			 
	cSREG = SREG;			/* store SREG value */
	asm("cli");

	//bit = ~bit ;
	//PinWR(LED_D1, &PORTC, bit) ;
	//TCNT3 =  t_1s ;
	//TCNT3 =  t_05s ;
	TCNT3 =  t_025s ;
		
	//time_Sec += 4 ;
	//if (time_Sec >= 60)
	//{
			//bit = ~bit ;
			//PinWR(LED_D1, &PORTC, bit) ;
		//time_Sec += (time_Sec - 60)  ;		
	//} 
	//else if (time_Sec == 60) time_Sec = 0  ;
	
	SREG = cSREG;			/* restore SREG value (I-bit) */
	asm("sei");
 }

/************************************************************************************
Timer3_OCR_init():
 ------------------
 Purpose: Sets up timer1 for nr of seconds  @ 11.0592Mhz Clock (mode 4)
			 Enables the Compare Interrupt.
 Parameters: None
 Returns:	None

 OCR1A: = (fclk/2 * Prescaler) * time 
 11.0592Mhz/1024 = 10800 * x = 1s: therefore x = 0x2A30
 e.g.  OCR1A = 0x3D08 -> Output Compare Register value for 1s.
  
  TOV3: = 1/(fclk/(1)Prescaler) * 0xFFFF(65535) = 6.068s
  [1/(11.0592Mhz/1024) = 1/10800 = 90.259us]
  [1/(11.0592Mhz/1024)/10500(0x2A30) = 1s]
  
  OCR3A: = (fclk/2 * Prescaler) * time
  (11.0592Mhz/(2*1024) = 5400 (0x1518)
  1s: 
  (11.0592Mhz/1024 = 10800 (92.5us) 
  1s * 10800Hz = 10800
  OCR1A = 0xDFD
 ************************************************************************************/
 void Timer3_CTC_init(void)
 {
	unsigned char cSREG;
		
	cSREG = SREG;			/* store SREG value */	
	asm("cli");

	tick_SEC = 0  ;
 
	TCNT3 = 0 ;			/* Reset counter */
	OCR3A = 10800 ;	/* 1s */
	//OCR3A = 5400 ;		/* 0.5s */
	//OCR3A = 2700 ;		/* 0.25s */
	//OCR3A = 1250 ;		/* 0.125s */
	 
	 /* Normal mode:
	 --------------- */	 
	TCCR3A = 0 ;	
	
	 /* Set prescaler to 1024 & CTC mode 
	 ----------------------------------- */	 
	TCCR3B = 0 ;
	TCCR3B |= (1 << CS32) | (1 << CS30) | (1 <<WGM32);	 // 1024 prescale & CTC mode enable	
	//TCCR3B |=  (1 << CS32) | (1 <<WGM32) ;					// 256 prescale & CTC mode enable

	TCCR3C = 0 ;
	
	 /* Timer/Counter3 Overflow interrupt is enabled.
	 ------------------------------------------------*/	 
	 TIMSK3 |= (1 << OCIE3A) ;		// Set interrupt on compare match	

	 SREG = cSREG;
	 asm("sei");	 
 }
 /************************************************************************************
 Timer3 Compare match Interrupt Service Routine():
 -------------------------------------------------
 Purpose: Interrupt Service Routine for Timer3 Compare Vector
			 Increments "time_Sec" value.
 Parameters: None
 Returns: None

 Note: OCFA is automatically cleared when the Output Compare Match A 
 Interrupt Vector is executed.
 ************************************************************************************/
 ISR (TIMER3_COMPA_vect)
 {
	//static UI_8 bit ;

	unsigned char cSREG;
			 
	cSREG = SREG;			/* store SREG value */
	asm("cli");
	
	tick_SEC++ ;

	SREG = cSREG;			/* restore SREG value (I-bit) */
	asm("sei");
 }
 /*******************************************************************
 The following code examples show how to perform an atomic read of the
 TCNTn Register contents. The OCRnA/B or ICRn Registers can be ready 
 by using the same principle.
 ********************************************************************/
#if 0
unsigned uint8_t TIM16_ReadTCNT3( void )
 {
	 unsigned char sreg;
	 unsigned uint8_t i;
	 /* Save global interrupt flag */
	 sreg = SREG;
	 /* Disable interrupts */
	 asm("cli");
	 /* Read TCNTn into i */
	 i = TCNT3;
	 /* Restore global interrupt flag */
	 SREG = sreg;
	 return i;
 }
#endif
   /************************************************************************************
 Seconds_update() using Timer3_TOV:
 -------------------------
 Purpose:	Sets up the nr of 10mS @ 16Mhz Clock (mode 4).
			
 Parameters: None
 Returns:	

1. Check for OCR3 flag - Output Compare match has occurred.
2. If yes increment seconds.
3. If the time_Sec are out more than 1s from the previous reading
	then do the adjustment by doing the following.
4. Check if TOV3 has occurred (timer has move on more that 4s).
5. Check how far TCNT3 counter value has moved on from the
	last check and determine the nr of seconds.
6. Add all the necessary seconds to the "time_Sec" variable.
 

TOV3 can be cleared by writing a logic one to its bit location.
 ************************************************************************************/
 //void Seconds_update(void)
 //{
	////static UI_16 n_prev;
	//UI_16 n  ;	
	////static UI_8 time_Sec_old ;
	////static UI_8 bit ;
	//unsigned char sreg;
//
	///* Save global interrupt flag */
	//sreg = SREG ;
	//asm("cli");
	///* Read TCNTn into n
	//--------------------- */
	//n = TCNT3;
//
	//SREG = sreg;
	////asm("sei");			/* Restore global interrupt flag */
	//#if 1
	////bit = ~bit ;
	////PinWR(YELLOW_LED5 , &PORTA, bit) ;
	////PinWR(GREEN_LED4 , &PORTA, ~bit) ;
	////delay_ms_1(500);
	//#endif
	///*------------------------------------------------
	  //START:
	//-------------------------------------------------*/
//
	 //if (n < t_2s && n > t_1s  )
	//{
		//if (n_prev == 0)
		//{
			//time_Sec ++ ;
			//n_prev = t_1s ;
		//}
		//
		//else if (n_prev == t_3s)
		//{
			//time_Sec = time_Sec + 2 ;
			//n_prev = t_1s ;
		//}
		//
		//else if (n_prev == t_2s)
		//{
			//time_Sec = time_Sec + 3 ;
			//n_prev = t_1s ;
		//}
	//}
	//
	//else if (n < t_3s && n > t_2s )
	//{	
		//if (n_prev == t_1s)
		//{
			//time_Sec ++ ;
			//n_prev = t_2s ;
		//}
		//
		//else if (n_prev == 0)
		//{
			//time_Sec = time_Sec + 2 ;
			//n_prev = t_2s ;
		//}
		//
		//else if (n_prev == t_3s)
		//{
			//time_Sec = time_Sec + 3 ;
			//n_prev = t_2s ;
		//}
	//}
	//
	//else if (n < t_4s && n > t_3s )
	//{
		//if (n_prev == t_2s)
		//{
			//time_Sec ++ ;
			//n_prev = t_3s ;
		//}
		//
		//else if (n_prev == t_2s)
		//{
			//time_Sec = time_Sec + 2 ;
			//n_prev = t_3s ;
		//}
		//
		//else if (n_prev == t_1s)
		//{
			//time_Sec = time_Sec + 3 ;
			//n_prev = t_3s ;
		//}
	//}
	//
	//else if (n < n_prev  && n < t_1s)
	//{
		//if (n_prev == t_3s)
		//{
			//time_Sec ++ ;
			//n_prev = 0 ;
		//}
		//
		//else if (n_prev == t_2s)
		//{
			//time_Sec = time_Sec + 2 ;
			//n_prev = 0 ;
		//}
		//
		//else if (n_prev == t_1s)
		//{
			//time_Sec = time_Sec + 3 ;
			//n_prev = 0 ;
		//}
	//}
//
//
		//if (time_Sec >= 60 + 3)
		//{
			//time_Sec = 3;
			//time_Min ++ ;
		//}
		//else if (time_Sec == 60 + 2)
		//{
			//time_Sec = 2;
			//time_Min ++ ;
		//}
		//else if (time_Sec == 60 + 1)
		//{
			//time_Sec = 1;
			//time_Min ++ ;
		//}
		//else if (time_Sec == 60)
		//{
			//time_Sec = 0;
			//time_Min ++ ;
		//}
		//
		//if (time_Min >= 60)
		//{
			//time_Min = 0;
			//time_Hour++;
		//}
		//if (time_Hour >= 24)
		//{
			//time_Hour = 0;
		//}
	//
	//sprintf(RTC_Buffer,  "%02d:%02d:%02d", time_Hour, time_Min, time_Sec);
	////lcd_display_string(RTC_Buffer, 5, LCD_LINE_4);
 //}
 #if 0
void Seconds_update(void)
 {
	static UI_16 n_prev, remainder_t ;
	UI_16 n , diff ;
	static UI_8 bit ;
	UI_8 lo, high ;
	char str[sizeof(uint8_t)*8+1] ;

	//n = TIM16_ReadTCNT3() ;
	unsigned char sreg;

	/* Save global interrupt flag */
	sreg = SREG;
	/* Disable interrupts */
	asm("cli");
	/* Read TCNTn into n
	--------------------- */
	n = TCNT3;
	high = TCNT3H ;
	lo = TCNT3L ;
	#if 0
	lcd_Clear() ;
	lcd_display_itoa ("TCNTL:", TCNT3, 0, LCD_LINE_2, 8);	
	lcd_display_itoa ("TCNTH:", TCNT3H, 0, LCD_LINE_1, 8);
	delay_ms_1(300);
	#endif

	SREG = sreg;
	asm("sei");			/* Restore global interrupt flag */

	#if 0
		//bit = ~bit ;
		//PinWR(YELLOW_LED5 , &PORTA, bit) ;
		//PinWR(GREEN_LED4 , &PORTA, ~bit) ;
		//delay_ms_1(500);
	#endif
	#if 1	
	itoa(high, str, 10) ;	//should return string "1567" 
	console_writeline("str ");
	itoa(lo, str, 10) ;	//should return string "1567"
	console_writeline("str ");		
	#endif
	/*------------------------------------------------
	  START:
	-------------------------------------------------*/
	//if (n_prev > n)
	//{
		//n_prev = 0 ;
	//}
	//diff = (n - n_prev) + remainder_t  ;	
//
	///*lcd_Clear() ;
	//lcd_display_itoa ("Diff:", diff, 0, LCD_LINE_2, 8);
	//delay_ms_1(300);*/
	//n_prev = n ;	/* Store current value of n */
//
	//if (diff < t_1s)
	//{
		//remainder_t += n ;		/* remainder for next time is the added to current value*/
		//diff += remainder_t ; /* Add remainder to current diff */
	//}
	//if (diff >= t_1s && diff < t_2s)
	//{	
		//time_Sec += 1 ;
		//remainder_t = n - t_1s ; /* remainder is value more than 1s */
	//}
	//else if (diff >= t_2s && diff < t_3s)
	//{
		//time_Sec += 2 ;
		//remainder_t = n - t_2s ;
	//}
	//else if (diff >= t_3s && diff < t_4s)
	//{
		//time_Sec += 3 ;
		//remainder_t = n - t_3s ;
	//}
	//else if (diff >= t_4s )
	//{
		//time_Sec += 4 ;
		//remainder_t = 0xFFFF - diff ;
		//n_prev = 0 ;
	//}
	//if (time_Sec >= 60)
	//{
			//time_Sec = 0 ;
			//time_Min ++ ;
	//}	
	//
//}
#endif
  /************************************************************************************
 Mills() using Timer3_TOV:
 -------------------------
 Purpose:	Sets up the nr of 10mS @ 16Mhz Clock (mode 4).
			
 Parameters: None
 Returns:	

1. Check for OCR3 flag - Output Compare match has occurred.
2. If yes increment seconds.
3. If the time_Sec are out more than 1s from the previous reading
	then do the adjustment by doing the following.
4. Check if TOV3 has occurred (timer has move on more that 4s).
5. Check how far TCNT3 counter value has moved on from the
	last check and determine the nr of seconds.
6. Add all the necessary seconds to the "time_Sec" variable.
 

TOV3 can be cleared by writing a logic one to its bit location.
 ************************************************************************************/

/************************************************************************************
 Timer0 Compare Interrupt Service Routine():
 -------------------------------------------
 Purpose: Interrupt Service Routine for Timer1 Compare Vector
 Parameters: None
 Returns: None
 Note: OCFA is automatically cleared when the Output Compare Match A 
 Interrupt Vector is executed.
 ************************************************************************************
 ISR (TIMER0_COMPA)
 {
	if ((TIFR0 & 0x01) == 1)
	{
		time_10ms ++ ;
		TIFR0 = 0x01; //clear timer1 overflow flag
	}
	if (time_10ms >= 100)
	{
		time_Sec ++ ;
		time_10ms = 0 ;
		if (time_Sec >= 255)
		{
			time_Sec = 0 ;
		}
	}
}

*/