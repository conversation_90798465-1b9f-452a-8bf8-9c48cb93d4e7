/*
 * Timer.c
 *
 * Created: 2018/08/08 03:29:02 PM
 *  Author: <PERSON><PERSON><PERSON>
 */ 
  
 #include <avr/io.h>
 #include <avr/interrupt.h>
 #include <stdlib.h>

#include "types.h"
#include "system.h"
#include "lcd.h"
#include "delay.h"
#include "usart.h"
#include "modem.h"
#include "io_pins.h"
#include "console.h"

/* For 11.0593MHz
------------------*/
#define t_025s 0xF573
#define t_05s 0xEAE7 
#define t_1s 0xD5CF 
#define t_2s 0xAB9F
#define t_3s 0x816F
#define t_4s 0x573F


/*-----------------------------------------
  FUNCTIONS PROTOTYPES
-------------------------------------------*/
void Timer1_init(uint8_t sec);
void Timer1_disable(void);
void Timer_set_Norm_Mod(void) ;

/*-------------------------------------------
 GLOBAL VARIABLES:
						 
Note:	
Global variables cannot be initialized because 
they only serve as a storage class.
-------------------------------------------*/
//volatile uint8_t tot_compA_cnt ;
volatile uint8_t time_Hour ;
volatile uint8_t time_Min ;
volatile uint8_t time_Sec ;
volatile uint16_t n_prev = 0;

extern volatile uint16_t tick_SEC ;

/************************************************************************************
 Timer_set_Norm_Mode():
 ---------------------
 Purpose:	Sets up timer1 for nr of seconds  @ 16Mhz Clock (mode 4)
			Enables the Compare Interrupt.
 Parameters: None
 Returns:	None

 OCR1A: = (fclk/2 * Prescaler) * time 
 16Mhz/1024 = 15625 * 3.5s = 0xD59F
 e.g.  OCR1A = 0x3D08 -> Output Compare Register value for 1s.
 ************************************************************************************/
 void Timer_set_Norm_Mod(void)
 {
	unsigned char cSREG;
		
	cSREG = SREG;			/* store SREG value */	
	asm("cli");
	
	 /* Normal mode: 
	 --------------- */
	 TCCR1A = 0 ; 
	 TCCR1B = 0 ;					// Mode 0, Free running
	 OCR1A = 0xFFFF;

	 TCCR1B |= (1 << CS12) | (1 << CS10);	// set prescaler to 1024 and start the timer	 

	 SREG = cSREG;
	 asm("sei");
	 
 }
 /************************************************************************************
 Timer1_Init():
 --------------
 Purpose:	Sets up timer1 for nr of seconds  @ 16Mhz Clock (mode 4)
			Enables the Compare Interrupt.
 Parameters: None
 Returns:	None

 OCR1A: = (fclk/2 * Prescaler) * time 
 16Mhz/1024 = 15625 * 3.5s = 0xD59F
 e.g.  OCR1A = 0x3D08 -> Output Compare Register value for 1s.

 ************************************************************************************/
 void Timer1_init(uint8_t sec)
 {
	unsigned char cSREG;
		
	cSREG = SREG;			/* store SREG value */	
	asm("cli");

	switch (sec)
	{
		case 1: 
			OCR1A = t_1s;
		break;
		case 2: 
			OCR1A = t_2s;
		break;
		case 3:
			OCR1A = 0xD59F;		// 3.5 sec
		break;
		case 4:
			OCR1A = 0xFFFF;		// 8.4 sec
		break;			
		default: 
			OCR1A = 0xFFFF;		// 8.4 sec
	}

	 /* Normal mode: 
	 --------------- */
	 TCCR1A = 0 ; 
	 TCCR1B |= (1 << WGM12);					// Mode 4, CTC on OCR1A	

	 TCCR1B |= (1 << CS12) | (1 << CS10);	// set prescaler to 1024 and start the timer	 

	 TIMSK1 |= (1 << OCIE1A) ;					// Set interrupt on compare match	

	 SREG = cSREG;
	 asm("sei");	 
 }
 
/************************************************************************************
 Timer Interrupt Service Routine():
 -----------------------------------
 Purpose: Interrupt Service Routine for Timer1 Compare Vector
 Parameters: None
 Returns: None
 Note: OCFA is automatically cleared when the Output Compare Match A 
 Interrupt Vector is executed.

 * Using "tot_compA_cnt" the Timer1 time can be extended.
 ************************************************************************************/
 //TODO Unused
 ISR (TIMER1_COMPA_vect)
 {
	unsigned char cSREG;

	cSREG = SREG;			/* store SREG value */
	SREG = cSREG;			/* restore SREG value (I-bit) */
 }

/************************************************************************************
 Timer_disable():
 -----------------------------------
 Purpose: Stops timer
 Parameters: None
 Returns: None
 ************************************************************************************/
void Timer1_disable(void)
{
	unsigned char cSREG;	

	cSREG = SREG;
	asm("cli");

	TIMSK1 &= ~(1 << OCIE1A);		/* Disable interrupt on compare match */

	TCCR1B = 0 ;	/* Stop the clock */
	TCNT1	 = 0 ;	/* Reset Counter  */

	SREG = cSREG;
}

 /************************************************************************************
Timer2_RTC_init():
 ------------------
 Purpose: Sets up timer2 for 1 second @32.768kHz Clock (normal mode).
 Parameters: None
 Returns:	None

 ************************************************************************************/
 void Timer2_RTC_init(void)
 {
	unsigned char cSREG;
		
	cSREG = SREG;			/* store SREG value */	
	asm("cli");

	  /* TC2 is enabled when (PRR0.PRTIM2) = '1' 
  ------------------------------------------*/
	PRR0 |= (1 << PRTIM2) ;		
	
	TCCR2A = 0 ;		/* Set for Normal mode */	
	TCCR2B = 0 ;

	/* Timer/Counter2 is clocked from a crystal connected 
		to the Timer Oscillator 1 (TOSC1) pin. 
	----------------------------------------------------*/
	ASSR = 0 ;
	//ASSR |= (1 << AS2) | (1 << EXCLK) ;		

	/* The prescaler is reset by writing a '1' 
	Allows presecaler to be written.
	------------------------------------------*/
	GTCCR |= (1 << PSRASY)  ; 

	/* Normal mode: Set prescaler to 256 with 256 run over = 1s
	----------------------------------------------------------- */	 
	TCCR2B |=  (1 << CS22) | (1 << CS21) ;

	/* Normal mode: Set Output Compare Register to 255 with 
	   256 run over = 1s for Output Compare Interrupt.
	----------------------------------------------------------- */
	OCR2A = 0xFF;
	OCR2B = 0xFF;			

	/* Timer/Counter2 Overflow interrupt is enabled 
	----------------------------------------------*/
	TIMSK2 |= (1 << TOIE2) ;		
				

	 SREG = cSREG;
	 asm("sei");	 
 }
 /************************************************************************************
 Timer2 Overflow Interrupt Service Routine():
 -------------------------------------------------
 Purpose: Interrupt Service Routine for 1 sec.
 Parameters: None
 Returns: None

 Note: TOV2 is automatically cleared when the Interrupt Vector is 
		 executed.
 ************************************************************************************/
 ISR (TIMER2_OVF_vect)
 {
	unsigned char cSREG;
	
	cSREG = SREG;			/* store SREG value */
	asm("cli");
	
	/* TOV bit is set when an overflow occurs in Timer/Counter2.
	------------------------------------------------------------*/	
	TIFR2 &= ~(1 << TOV2) ;
		
	SREG = cSREG;			/* restore SREG value (I-bit) */
	asm("sei");
 }

/************************************************************************************
Timer2_COMPA_init():
 --------------
 Purpose:	Sets up timer1 for nr of seconds  @ 16Mhz Clock (mode 4)
			Enables the Compare Interrupt.
 Parameters: None
 Returns:	None

 OCR1A: = (fclk/2 * Prescaler) * time 
 11.0592Mhz/1024 = 10800 (0x2A30)
 1s: OCRA = 0x2A30
 ************************************************************************************/
 void Timer2_COMPA_init(void)
 {
	unsigned char cSREG;
		
	cSREG = SREG;			/* store SREG value */	
	asm("cli");

	OCR2A = 0xFF ;		// ?????????? sec

	 /* Timer Compare mode: 
	 ---------------------- */
	 TCCR2A = 0 ; 
	 TCCR2A |= (1 << WGM21);					// Mode 2, CTC on OCR2A	

	 TCCR2B |= (1 << CS22) | (1 << CS21) | (1 << CS20);	// set prescaler to 1024 and start the timer	 

	 TIMSK2 |= (1 << OCIE2A) ;					// Set interrupt on compare match	

	// GTCCR |= (1 << TSM) ;

	 SREG = cSREG;
	 asm("sei");
	 
 }
 /************************************************************************************
Timer/Counter2 Compare Match A Interrupt Service Routine():
 ---------------------------------------------------------
 Purpose: Interrupt Service Routine for 1 sec.
 Parameters: None
 Returns: None

 Note: TOV2 is automatically cleared when the Interrupt Vector is 
		 executed.
 ************************************************************************************/
 ISR (TIMER2_COMPA_vect)
 {
	unsigned char cSREG;
	
	cSREG = SREG;			/* store SREG value */
	asm("cli");
	
	/* TOV bit is set when an overflow occurs in Timer/Counter2.
	------------------------------------------------------------*/	
	TIFR2 &= ~(1 << TOV2) ;

	//bit = ~bit ;
	//PinWR(GREEN_LED5 , &PORTA, bit) ;
		
	SREG = cSREG;			/* restore SREG value (I-bit) */
	asm("sei");
 }
 /*******************************************************************************************
 Timer3_Norm_Init():
 -----------------
 Purpose:	Sets up timer3 for free running mode @ 16Mhz Clock (mode 1)
			   
 Parameters: None
 Returns:	None

 TOV3: = 1/(fclk/(1)Prescaler) * 0xFFFF(65535) = 6.068s			
			[1/(11.0592Mhz/128)/65535(0xFFFF) = 1.318s]
1s:
	11.0592Mhz/1024 = 10800Hz(92.592 us)
	1s/92.592us = 10800
	65535(0xFFFF) - 10800 = 54735 (0xD5CF)
0.5s:
	0.5s/92.592us = 5400
	65535(0xFFFF) - 5400 = 60135 (0xEAE7)
 ************************************************************************************/
 void Timer3_Norm_init(void)
 {
	unsigned char cSREG;
		
	cSREG = SREG;			/* store SREG value */	
	asm("cli");	

	/* Set Normal mode & prescaler to 1024.
	------------------------------------- */
	TCCR3A = 0 ;				// Normal MODE, not for waveform generation.
	TCCR3B |= (1 << CS32) | (1 << CS30) ;	 
	
	TCNT3 =  t_025s ;

	TIMSK3 |= (1 << TOIE3) ;		// Set interrupt on overflow	

	SREG = cSREG;
	asm("sei");
	 
 }
 /************************************************************************************
 Timer3 Compare match Interrupt Service Routine():
 -------------------------------------------------
 Purpose: Interrupt Service Routine for Timer3 Compare Vector
			 Increments "time_Sec" value with 4 sec.
 Parameters: None
 Returns: None

 Note: TOV3 is automatically cleared when the Interrupt Vector is 
		 executed.
 ************************************************************************************/
 ISR (TIMER3_OVF_vect)
 {
	unsigned char cSREG;
			 
	cSREG = SREG;			/* store SREG value */
	asm("cli");

	TCNT3 =  t_025s ;
	
	SREG = cSREG;			/* restore SREG value (I-bit) */
	asm("sei");
 }

/************************************************************************************
Timer3_OCR_init():
 ------------------
 Purpose: Sets up timer1 for nr of seconds  @ 11.0592Mhz Clock (mode 4)
			 Enables the Compare Interrupt.
 Parameters: None
 Returns:	None

 OCR1A: = (fclk/2 * Prescaler) * time 
 11.0592Mhz/1024 = 10800 * x = 1s: therefore x = 0x2A30
 e.g.  OCR1A = 0x3D08 -> Output Compare Register value for 1s.
  
  TOV3: = 1/(fclk/(1)Prescaler) * 0xFFFF(65535) = 6.068s
  [1/(11.0592Mhz/1024) = 1/10800 = 90.259us]
  [1/(11.0592Mhz/1024)/10500(0x2A30) = 1s]
  
  OCR3A: = (fclk/2 * Prescaler) * time
  (11.0592Mhz/(2*1024) = 5400 (0x1518)
  1s: 
  (11.0592Mhz/1024 = 10800 (92.5us) 
  1s * 10800Hz = 10800
  OCR1A = 0xDFD
 ************************************************************************************/
 void Timer3_CTC_init(void)
 {
	unsigned char cSREG;
		
	cSREG = SREG;			/* store SREG value */	
	asm("cli");

	tick_SEC = 0  ;
 
	TCNT3 = 0 ;			/* Reset counter */
	OCR3A = 10800 ;	/* 1s */
	//OCR3A = 5400 ;		/* 0.5s */
	//OCR3A = 2700 ;		/* 0.25s */
	//OCR3A = 1250 ;		/* 0.125s */
	 
	 /* Normal mode:
	 --------------- */	 
	TCCR3A = 0 ;	
	
	 /* Set prescaler to 1024 & CTC mode 
	 ----------------------------------- */	 
	TCCR3B = 0 ;
	TCCR3B |= (1 << CS32) | (1 << CS30) | (1 <<WGM32);	 // 1024 prescale & CTC mode enable	
	//TCCR3B |=  (1 << CS32) | (1 <<WGM32) ;					// 256 prescale & CTC mode enable

	TCCR3C = 0 ;
	
	 /* Timer/Counter3 Overflow interrupt is enabled.
	 ------------------------------------------------*/	 
	 TIMSK3 |= (1 << OCIE3A) ;		// Set interrupt on compare match	

	 SREG = cSREG;
	 asm("sei");	 
 }

 ISR (TIMER3_COMPA_vect)
 {
	unsigned char cSREG;
			 
	cSREG = SREG;
	asm("cli");
	
	tick_SEC++ ;

	SREG = cSREG;
	asm("sei");
}
