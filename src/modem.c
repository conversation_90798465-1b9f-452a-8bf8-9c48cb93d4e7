/*
 * Modem.c
 *
 * Created: 2018/07/14 10:55:14 AM
 *  Author: TXDev systems
 *
 *	Application note: https://www.waveshare.com/wiki/GSM/GPRS/GNSS_HAT
 */ 
#include <avr/io.h>
#include <stdlib.h> 
#include <string.h>

#include "types.h"
#include "system.h"
#include "delay.h"
#include "usart.h"
#include "timer.h"
#include "io_pins.h"
#include "console.h"

#include "modem.h"

 void modem_power_off(void)
 {
 	uint8_t status ; 	
	
	/* Set port direction to inputs 	
	--------------------------------*/
	status = PinRD(SIM800_STAT, SIM800_PORT) ;

	if (status == 1)
 	{
		/* Normal operation */
	    PinWR(SIM868_PWRKEY, &PORTD, 1) ;	/* __/^^^^^^^^^^^^^^^^^^^  */
	    delay_ms(1000) ;
		PinWR(SIM868_PWRKEY, &PORTD, 0) ;	/* /^^^^^^\____ for > 1.5s */
		delay_ms(2000) ;
		PinWR(SIM868_PWRKEY, &PORTD, 1) ;	/* __/^^^^^^^^^^^^^^^^^^^  */
		delay_ms(2000) ;		
	}
	DDRA &= ~(1 << SIM868_PWRKEY)  ; // Configuring PWR_KEY as input
 }

bool modem_power_on(void)
{
	uint8_t retry = 0;	

	usart0_reset_buffer();
	
	/* Powers the Modem P-Chan FET 
	-------------------------------*/	
	PinWR(SIM868_PWRKEY, &PORTD, 1) ;	/* __/^^^^^^^^^  */ 
	delay_ms(500);

	/* Power key
	-------------*/	
	PinWR(SIM868_PWRKEY, &PORTD, 0) ;		/* __/^^^^^^\____ low for 1.2s */			
	while (!modem_is_power_on())
	{
		delay_ms(100);
		retry++ ;
		if (retry > 50)
		{
			return false ;
		}
	} 	
	PinWR(SIM868_PWRKEY, &PORTD, 1) ;		/* __/^^^^^^\_______/^^^^^^ */	

	return true ;				
}		

bool modem_is_power_on (void)
{
	return PinRD(SIM800_STAT, SIM800_PORT) == 1;
}
 
bool modem_init(void)
{
	usart0_init(BR115200) ;	
	if (usart0_send_command("AT+IPR=115200", "OK") == false)
	{
		console_writeline("Modem IPR failed");
		return false;
	}

	if (usart0_send_command("AT", "OK") == false)
	{
		console_writeline("Modem AT failed");
		return false;
	}

	if (usart0_send_command("AT+CMEE=2", "OK") == false)
	{
		console_writeline("Modem CMEE failed");
		return false;
	}
	
	if (usart0_send_command("AT+CREG=1", "OK") == false)
	{
		console_writeline("Modem CREG=1 failed");
		return false;
	}

	if (usart0_send_command("AT+CREG=0", "OK") == false)
	{
		console_writeline("Modem CREG=0 failed");
		return false;
	}

	return true;
}

uint8_t modem_RSSI(void)
{
	int rssi;
	int ber;
	static char buffer[16];

	if (usart0_read_data("AT+CSQ", "OK", buffer, sizeof(buffer)) == false)
		return 99;

	if (sscanf(buffer, "+CSQ: %d,%d", &rssi, &ber) != 2)
		return 99;

	return rssi;
}

const char *modem_IMEI(void)
{
	static char IMEI[IMEI_LNGTH + 1] = {0};

	console_writeline("Read IMEI from modem:");
	if (usart0_read_data("AT+GSN", "OK", IMEI, sizeof(IMEI)) == false)
	{
		console_writeline("Failed to get IMEI from modem");
		return NULL;
	}
	IMEI[IMEI_LNGTH] = '\0';

	console_write("IMEI Modem:  [");
	console_write(IMEI);
	console_writeline("]");
	
	return &IMEI[0];
}

const char *modem_ICCID(void)
{
	static char ICCID[ICCID_LNGTH + 1] = {0};

	console_writeline("Read ICCID from modem:");
	if (usart0_read_data("AT+CCID", "OK", ICCID, sizeof(ICCID)) == false)
	{
		console_writeline("Failed to get ICCID from modem");
		return NULL;
	}
	ICCID[ICCID_LNGTH] = '\0';

	console_write("ICCID Modem:  [");
	console_write(ICCID);
	console_writeline("]");

	return &ICCID[0];
}