/*
 * Modem.c
 *
 * Created: 2018/07/14 10:55:14 AM
 *  Author: TXDev systems
 *
 *	Application note: https://www.waveshare.com/wiki/GSM/GPRS/GNSS_HAT
 */ 
#include <avr/io.h>
#include <stdlib.h> 
#include <string.h>

#include "types.h"
#include "system.h"
#include "lcd.h"
#include "delay.h"
#include "usart.h"
#include "timer.h"
#include "interrupts.h"
#include "e2prom.h"
#include "io_pins.h"
#include "mqtt.h"
#include "console.h"

#define RXBUFF_LENGTH	70		/* modified from 70*/
#define DISPLAY_LENGTH	80
#define BUFF_LENGTH		60

 /*------------------------------------------------------------------------------
                         External Variables
------------------------------------------------------------------------------*/
interruptflags intrflags ;

 /*------------------------------------------------------------------------------
                         LOCAL FUNCTIONS PROTOTYPES
------------------------------------------------------------------------------*/
UI_8 Modem_init(void) ;

UI_8 CheckModemStat( void) ;
UI_8 Wait_for_OK(void) ;
UI_8 SMS_Await_Incoming(UI_8 disply, UI_8 chars) ;
UI_8 SMS_Get_Message(char *message, char info[CELLNR]);
void Long_display(char buff[BUFF_LENGTH], uint8_t nr) ;
UI_8 Modem_Pwr_ON(void) ;
void Modem_Pwr_OFF(void) ;
void Error_Disply(char *dsplymsg,UI_8 row);
char * Check4string(char strng[]) ;
UI_8 Status_Pin_Check (void) ;

/*-----------------------------------------------------------
  Function   : Modem_Pwr_OFF
  Purpose    : Powers the Modem.
  Parameters : 
  Returns    : Nothing
  Notes      : 
  DDRB  |= 1<<5 - Configuring PB5 as Output
  PORTB |= 1<<5 - Writing '1' to PB5
  PORTB &= ~(1 << pin) - clears bit
------------------------------------------------------------*/
 void Modem_Pwr_OFF(void)
 {
 	UI_8 status ; 	
	
	/* Set port direction to inputs 	
	--------------------------------*/
	status = PinRD(SIM800_STAT, SIM800_PORT) ;

	if (status == 1)
 	{
		/* Normal operation */
	    PinWR(SIM868_PWRKEY, &PORTD, 1) ;	/* __/^^^^^^^^^^^^^^^^^^^  */
	    delay_ms_1(1000) ;
		PinWR(SIM868_PWRKEY, &PORTD, 0) ;	/* /^^^^^^\____ for > 1.5s */
		delay_ms_1(2000) ;
		PinWR(SIM868_PWRKEY, &PORTD, 1) ;	/* __/^^^^^^^^^^^^^^^^^^^  */
		delay_ms_1(2000) ;		
	}
	DDRA &= ~(1 << SIM868_PWRKEY)  ; // Configuring PWR_KEY as input
 }

/*-----------------------------------------------------------
  Function   : Modem_Pwr_ON
  Purpose    : Powers the Modem.
  Parameters : 
  Returns    : Nothing
  Notes      : 
------------------------------------------------------------*/
 UI_8 Modem_Pwr_ON(void)
{
	UI_8 retry = 0;	
	
	/* Powers the Modem P-Chan FET 
	-------------------------------*/	
	PinWR(SIM868_PWRKEY, &PORTD, 1) ;	/* __/^^^^^^^^^  */ 
	delay_ms_1(500);

	/* Power key
	-------------*/	
	PinWR(SIM868_PWRKEY, &PORTD, 0) ;		/* __/^^^^^^\____ low for 1.2s */			
	while (Status_Pin_Check() == SYS_FAIL)
	{
		delay_ms_1(100);
		retry++ ;
		if (retry > 50)
		{
			return SYS_FAIL ;
		}
	} 	
	PinWR(SIM868_PWRKEY, &PORTD, 1) ;		/* __/^^^^^^\_______/^^^^^^ */	

	return SYS_PASS ;				
}		

/*-----------------------------------------------------------
  Function   : Status_Pin_Check
  Purpose    : Powers the Modem.
  Parameters : 
  Returns    : Nothing
  Notes      : 
------------------------------------------------------------*/
UI_8 Status_Pin_Check (void)
{
	UI_8 status;
	
	status = PinRD(SIM800_STAT, SIM800_PORT) ;

	return (status == 1) ? SYS_PASS : SYS_FAIL;
}
 
/*-----------------------------------------------------------
  Function   : Modem_init
  Purpose    : Initiates the Modem.
  Parameters : 
  Returns    : Nothing
  Notes      : A HEX string such as "00 49 49 49 49 FF FF FF FF" will be sent
				out through serial port at the baud rate of 115200 immediately 
				after SIM800 Series is powered on.
				Only enter AT Command through serial port after SIM800 Series is
				powered on and Unsolicited Result Code "RDY" is received from 
				serial port.
------------------------------------------------------------*/
 UI_8 ModemInit(void)
{
	/* Set UART 1 Baudrate = 115.2K*/	
	USART0_Init(BR115200) ;	
	if (USART0_SendCommand("AT+IPR=115200", "OK") == SYS_FAIL)
	{
		console_writeline("Modem IPR failed");
		return SYS_FAIL;
	}

	/* Confirm baudrate is good with AT / OK check*/
	if (USART0_SendCommand("AT", "OK") == SYS_FAIL)
	{
		console_writeline("Modem AT failed");
		return SYS_FAIL;
	}

	if (USART0_SendCommand("AT+CMEE=2", "OK") == SYS_FAIL)
	{
		console_writeline("Modem CMEE failed");
		return SYS_FAIL;
	}
	
	if (USART0_SendCommand("AT+CREG=1", "OK") == SYS_FAIL)
	{
		console_writeline("Modem CREG=1 failed");
		return SYS_FAIL;
	}

	if (USART0_SendCommand("AT+CREG=0", "OK") == SYS_FAIL)
	{
		console_writeline("Modem CREG=0 failed");
		return SYS_FAIL;
	}

	return SYS_PASS;
}

UI_8 Modem_USSD_GetDataBalance()
{
	if (USART0_SendCommand("AT+CUSD=1,\"*136#\"", "OK") == SYS_FAIL)
		return SYS_FAIL;

	if (USART0_ParseDataBalance() == SYS_FAIL)
		return SYS_FAIL;

	if (USART0_SendCommand("AT+CUSD=2", "OK") == SYS_FAIL)
		return SYS_FAIL;

	return SYS_PASS;
}

 /*-----------------------------------------------------------
  Function   : FindServiceProv
  Purpose    :
  Parameters : 
  Returns    : 
  Notes      :
------------------------------------------------------------*/
 UI_8 FindServiceProv (mqtt_data_struct *mqtt_dataptr)
 {
#if 0
	char *ptr = NULL;
	UI_8 i = 0 ;
	/* --------------------------------
		*PSUTTZ: 2021,8,3,13,47,55,"+8",0
		DST: 0
		+CIEV: 10,"65510","MTN-SA","MTN-SA", 0, 0
	-----------------------------------*/
	while (1)
	{		
		USART1_ClearBuffer();
		USART0_SendString("AT+CLTS=1\n\r");	
		if (Wait_for_OK() ==  SYS_PASS)
		{			
			USART1_ClearBuffer();
			if (USART1_Wait4RxBuf2Fill(70,1) == SYS_PASS)	
			{
				ptr = Check4string("+CIEV:") ;			
				if (ptr != NULL)
				{
					console_writeline("\n\rISP: ");
					ptr = Check4string("+CIEV:") ;
					for (UI_8 n = 0, i = 19; i < 25; n++, i++)
					{
						mqtt_dataptr->ISP[n] = *(ptr + i) ;
					}
					mqtt_dataptr->ISP[6] = '\0' ;
					console_writeline(mqtt_dataptr->ISP) ;
					console_writeline("\n\n\r");
					delay_ms_1(10000);
					return SYS_PASS ;
				}	
				else i++ ;
				delay_ms_1(1000);
			}		
			else i++ ;			
		} 
		else i++ ;
		
		if (i > 2)
		{
			break ;
		}
	}

	console_writeline("\n\rISP retrieval failed\n\r") ;
	strcpy(mqtt_dataptr->ISP, "ISP=??") ;
	//mqtt_dataptr->ISP[6] = '\0' ;
#endif
	return SYS_FAIL ;	
}
 /*-----------------------------------------------------------
  Function   : FindSigQ
  Purpose    :
  Parameters : 
  Returns    : 
  Notes      :
------------------------------------------------------------*/
UI_8 FindSigQ(mqtt_data_struct *mqtt_dataptr)
{
	char buffer[16];

	if (USART0_ReadData("AT+CSQ", "OK", buffer, sizeof(buffer)) == SYS_FAIL)
		return SYS_FAIL;

	if (sscanf(buffer, "+CSQ: %s", mqtt_dataptr->SignalStrng) != 1)
		return SYS_FAIL;

	char *comma = strchr(mqtt_dataptr->SignalStrng, ',');
	if (comma != NULL)
		*comma = '\0';

	return SYS_PASS;
}

/*-----------------------------------------------------------
  Function   : Send_AT_Cmd
  Purpose    : Sends the "AT" command to init the sending of the modem comms.
  Parameters : 
  Returns    : Pass or fail.
  Notes      :
------------------------------------------------------------*/
 UI_8 Send_AT_Cmd(mqtt_data_struct *mqtt_dataptr)
 {
	if (USART0_SendCommand("AT", "OK") == SYS_FAIL)
	{
		console_writeline("Modem AT failed\r\n");
		return SYS_FAIL;
	}

	return SYS_PASS;
 }
	
const char *Modem_IMEI(void)
{
	static char IMEI[IMEI_LNGTH + 1] = {0}; // +1 for null terminator

	console_writeline("Read IMEI from modem:");
	if (USART0_ReadData("AT+GSN", "OK", IMEI, sizeof(IMEI)) == SYS_FAIL)
	{
		console_writeline("Failed to get IMEI from modem");
		return NULL;
	}
	IMEI[IMEI_LNGTH] = '\0';

	console_write("IMEI Modem:  [");
	console_write(IMEI);
	console_writeline("]");
	
	/*-----------------------------------------
		Check stored IMEI with received one.
		If they aren't the same write new one into EEPROM.
	--------------------------------------------*/
	//TOOD: Move to main function
#if 0
	UI_16 addr = 0 ;
	UI_8 i = 0 ;	
	addr = IMEI_EEPROM ;
	for (i = 0; i<IMEI_LNGTH; i++)
	{
		if (IMEI[i] != EEPROM_RD_Char(addr+i))
		{
			console_writeline("*** Write new IMEI to EEPROM ***");
			EEPROM_WR_string(IMEI_EEPROM, IMEI, IMEI_LNGTH) ;
			break ;
		}
	}
	console_write("IMEI EEPROM: [");
	for (i = 0; i<IMEI_LNGTH; i++)
	{
		console_write("%c", EEPROM_RD_Char(IMEI_EEPROM + i)) ;
	}
	console_writeline("]");
#endif
	return &IMEI[0];
}

/*-----------------------------------------------------------
  Function   : Modem_ICCID
  Purpose    : Retrieves ICCID from modem 

  Parameters : 
  Returns    : Pass or fail.
  Notes      :
------------------------------------------------------------*/
const char *Modem_ICCID(void)
{
	static char ICCID[ICCID_LNGTH + 1] = {0}; // +1 for null terminator

	console_writeline("Read ICCID from modem:");
	if (USART0_ReadData("AT+CCID", "OK", ICCID, sizeof(ICCID)) == SYS_FAIL)
	{
		console_writeline("Failed to get ICCID from modem");
		return NULL;
	}
	ICCID[ICCID_LNGTH] = '\0';

	console_write("ICCID Modem:  [");
	console_write(ICCID);
	console_writeline("]");
	return &ICCID[0];

 /*
	EEPROM_WR_string(ICCID_EEPROM, mqtt_dataptr->ICCID, ICCID_LNGTH) ;
	#if 1
	console_writeline("\n\n\rICCID read:\n\r-----------");
	console_writeline("\n\rICCID: ");
	//console_writeline(mqtt_dataptr->ICCID);
	#endif
	for (n = 0; n <= ICCID_LNGTH; n++)
	{
		USART1_Send_char(EEPROM_RD_Char(ICCID_EEPROM + n)) ;
	}		
	return SYS_PASS;
*/
}

/******************************************************************
If "Wait for OK" failed, this function displays the error that 
occurred.
*******************************************************************/
//void Error_Disply(char *dsplymsg, UI_8 row)
//{
	////lcd_Clear();
	////lcd_display_string(dsplymsg, 0, row);
	////delay_ms_1(2000);
	//Timer1_disable() ;
	////Modem_Pwr_OFF() ;	
//}

#if 0
 /***********************************************************************
 * Function:   NMEAD_display                             
 * --------------------------
 * Displays the contents of 'buff' array over all 4 rows of the LCD.
 **********************************************************************/
void LCD_long_display(char *data)
{
	 UI_8 i, nr = DISPLAY_LENGTH ;
	 UI_8 debug = 0 ;
	 
	 asm("cli");
	 if (debug)
	 {
		 //lcd_Clear();
		 //lcd_display_itoa ("nr:", nr, 0, LCD_LINE_4, 9);
		 //lcd_SetCursorPos(0, 1);
		 //lcd_Txt("Got to display!", true);
		 //delay_ms_1(1000);
	 }
	 //lcd_Clear();

	 i = 0 ;
	 while(i < nr)
	 {
		 if (i < 20)
		 {
			 lcd_SetCursorPos(0, 0);
			 while (i < 20 && i < nr)
			 {
				 lcd_Char(data[i], true );
				 i++ ;
			 }
		 }
		 if (i > 19 && i < 40)
		 {
			 lcd_SetCursorPos(0, 1);
			 while (i < 40 && i < nr)
			 {
				 lcd_Char(data[i], true );
				 i++ ;
			 }
		 }
		 if (i > 39 && i < 60)
		 {
			 lcd_SetCursorPos(0, 2);
			 while (i < 60 && i < nr)
			 {
				 lcd_Char(data[i], true );
				 i++ ;
			 }
		 }
		 if (i > 59 && i < 80)
		 {
			 lcd_SetCursorPos(0, 3);
			 while (i < 80 && i < nr)
			 {
				 lcd_Char(data[i], true );
				 i++ ;
			 }
		 }
	 }
	 delay_ms_1(5000);	 
}
#endif 
