/*
 * OW_DS1820.c
 *
 * Created: 2020/04/30 12:48:14 PM
 * Amended 2021/2/26
 *  Author: <PERSON><PERSON><PERSON>
 */ 
 // ---------------------------------------------------------------------------
 // "ONE-WIRE" ROUTINES
 //
 #include <avr/io.h>
 #include <stdlib.h>
 #include <stdio.h>
 #include <string.h>
 #include <avr/pgmspace.h>
 #include <math.h>
 #include "console.h"
 #include "types.h"
 #include "system.h" 
 #include "delay.h"
 #include "io_pins.h"
 #include <util/delay.h> // used for _delay_us function
 #include "temperature.h"

 #define THERM_PORT PORTB
 #define THERM_DDR DDRB
 #define THERM_PIN PINB
 #define THERM_IO 4
 #define THERM_INPUT() ClearBit(THERM_DDR,THERM_IO) // make pin an input
 #define THERM_OUTPUT() SetBit(THERM_DDR,THERM_IO) // make pin an output
 #define THERM_LOW() ClearBit(THERM_PORT,THERM_IO) // take (output) pin low
 #define THERM_HIGH() SetBit(THERM_PORT,THERM_IO) // take (output) pin high
 #define THERM_READ() ReadBit(THERM_PIN,THERM_IO) // get (input) pin value
 #define THERM_CONVERTTEMP 0x44
 #define THERM_READSCRATCH 0xBE
 #define THERM_WRITESCRATCH 0x4E
 #define THERM_COPYSCRATCH 0x48
 #define THERM_READPOWER 0xB4
 #define THERM_SEARCHROM 0xF0
 #define THERM_READROM 0x33
 #define THERM_MATCHROM 0x55
 #define THERM_SKIPROM 0xCC
 #define THERM_ALARMSEARCH 0xEC
 #define CONV_9BIT	0x1F
 #define CONV_10BIT	0x3F
 #define CONV_11BIT	0x5F
 #define CONV_12BIT	0x7F
 #define BIT_9		5
 #define BIT_10	25
 #define BIT_11	125
 #define BIT_12	625

 #define FALSE 0
 #define TRUE 1
 #define NEGATIVE 0
 #define POSITIVE 1

 #define ClearBit(x,y) x &= ~_BV(y) // equivalent to cbi(x,y)
 #define SetBit(x,y) x |= _BV(y)			// equivalent to sbi(x,y)
 #define ReadBit(x,y) x & _BV(y)		// call with PINx and bit#

 //#define DQ_RD PinRD(PIND5, PIND) 
 //#define DQ_WR_1 PinWR(THERM_IO, &THERM_PORT, 1)
 //#define DQ_WR_0 PinWR(THERM_IO, &THERM_PORT, 0)

 typedef uint8_t byte; // I just like byte & sbyte better
 typedef int8_t sbyte;
 /* Prototypes
 --------------*/
 UI_8 ds1820_Reset();
 UI_8 ds1820_ReadBit();
 UI_8 ds1820_ReadByte();
 void RomReaderProgram();
 void ds1820_ReadTempC(int16_t *temperature); // Changed from int8_t to int16_t
 void ds1820_MatchRom(UI_8 rom[]);
 void ds1820_ReadTempRaw(UI_8 id[], UI_8 *t0, UI_8 *t1);
 void ds1820_WriteBit(UI_8 bit);
 void ds1820_WriteByte(UI_8 data);
 void ds1820_Initialise(void) ;
 void ROM_Search(void) ;
 //void DS1820_Test(void);
 UI_8 OWI_ComputeCRC8(UI_8 inData, UI_8 seed) ;

 /* UI_8 read_bit(void);
  void write_bit(UI_8 bitval); */
  UI_8 read_byte(void);
  void write_byte(UI_8 val);
  void Read_Temperature(void);
  UI_8 ow_reset(void);
  UI_8 First(UI_8 lastDiscrep, UI_8 doneFlag, UI_8 ROM[]) ;
  UI_8 Next(UI_8 lastDiscrep, UI_8 doneFlag, UI_8 ROM[]) ;
  void ow_crc( UI_8 x, UI_8 dowcrc) ;

 // the following arrays specify the addresses of *my* ds18b20 devices
 // substitute the address of your devices before using.
 //UI_8 rom0[] = {0x28, 0xFF, 0x61, 0x0F, 0xA0, 0x16, 0x04, 0xDF};
 //UI_8 rom0[] = {0x28, 0xFF, 0xE3, 0x64, 0xA0, 0x16, 0x05, 0xCD} ;
 UI_8 rom0[10] ;
 const UI_8 dscrc_table[] PROGMEM = {
	 0, 94,188,226, 97, 63,221,131,194,156,126, 32,163,253, 31, 65,
	 157,195, 33,127,252,162, 64, 30, 95, 1,227,189, 62, 96,130,220,
	 35,125,159,193, 66, 28,254,160,225,191, 93, 3,128,222, 60, 98,
	 190,224, 2, 92,223,129, 99, 61,124, 34,192,158, 29, 67,161,255,
	 70, 24,250,164, 39,121,155,197,132,218, 56,102,229,187, 89, 7,
	 219,133,103, 57,186,228, 6, 88, 25, 71,165,251,120, 38,196,154,
	 101, 59,217,135, 4, 90,184,230,167,249, 27, 69,198,152,122, 36,
	 248,166, 68, 26,153,199, 37,123, 58,100,134,216, 91, 5,231,185,
	 140,210, 48,110,237,179, 81, 15, 78, 16,242,172, 47,113,147,205,
	 17, 79,173,243,112, 46,204,146,211,141,111, 49,178,236, 14, 80,
	 175,241, 19, 77,206,144,114, 44,109, 51,209,143, 12, 82,176,238,
	 50,108,142,208, 83, 13,239,177,240,174, 76, 18,145,207, 45,115,
	 202,148,118, 40,171,245, 23, 73, 8, 86,180,234,105, 55,213,139,
	 87, 9,235,181, 54,104,138,212,149,203, 41,119,244,170, 72, 22,
	 233,183, 85, 11,136,214, 52,106, 43,117,151,201, 74, 20,246,168,
 116, 42,200,150, 21, 75,169,247,182,232, 10, 84,215,137,107, 53};
 
/*************************************************************
NOTE: For temperature decimal resolution the following are defined:-
 #define CONV_9BIT 0x1F - 0.5 �C
 #define CONV_10BIT 0x3F - 0.25 �C
 #define CONV_11BIT 0x5F - 0.125 �C
 #define CONV_12BIT 0x7F - 0.0625 �C
  
Currently the configuration is for 11 bit resolution.
To change, find all referrals to "CONV_11BIT" and change to
desired value.

Also change the dec_resolution in ds1820_ReadTempC() accordingly:-
#define BIT_9		5
#define BIT_10	25
#define BIT_11	125
#define BIT_12	625
*************************************************************
 ds1820_Initialise()
 -------------------
 Write to byte 4 in scratch-pad area to set device for 9 bit 
 resolution. Note bit msb15 is the Sign bit (& with 0x80)
 NOTE: Configured for 11bits resolution!
**************************************************************/
void ds1820_Initialise(void)
{
	UI_8 i = 0 ;
	UI_8 RD_byte[10] ;
	
	console_writeline("DS1820 Initialisation:");
	//if ((ds1820_Reset()) == SYS_FAIL)
	//{
		 //console_writeline("\n\rReset failed");
		 //HALT
	//}
	/* Skip ROM & start temperature conversion  
	------------------------------------------*/
	ds1820_Reset();
	ds1820_WriteByte(THERM_SKIPROM);
	ds1820_WriteByte(THERM_WRITESCRATCH);

/* Starts write from byte nr. 2, 0 & 1 are read only */
	ds1820_WriteByte(0x00);				/* byte 2 */
	ds1820_WriteByte(0x00);				/* byte 3 */
	//ds1820_WriteByte(CONV_9BIT) ;		/* byte 4 */
	ds1820_WriteByte(CONV_11BIT) ;		/* byte 4 */

	/* Verify write to scratch pad memory */	
	ds1820_Reset();
	ds1820_WriteByte(THERM_SKIPROM);
	ds1820_WriteByte(THERM_READSCRATCH) ;		/* byte 4 */
	for (i = 0; i<5; i++)
	{
		RD_byte[i] = ds1820_ReadByte(); 
	}
	if (RD_byte[4] == CONV_11BIT)
	{
		console_writeline("Config verify passed.");
	}
	else
	{
		console_write("Scratch memory content: [");
		for (i = 0; i<10; i++)
		{
			console_write(" %d", RD_byte[i]);
		}
		console_writeline("]");
	}	
}
/**************************************************************/ 
 //UI_8 _ds1820_Reset(void)
 //{
	 //UI_8 result = 0 ;
	 //UI_8 loop = 0 ;
//
	//while (loop < 100)
	//{
		 //THERM_OUTPUT(); // set pin as output
		 //THERM_LOW(); // pull pin low for 480uS
		 //_delay_us(480);
		 //THERM_INPUT(); // set pin as input
		 ////_delay_us(60); // wait for 60uS
		 //if (!(THERM_READ()))
		 //{
			//return SYS_PASS;
		 //}
		 //while (THERM_READ())
		 //{
			//result ++ ;
			//if (result > 60)
			//{
				//break ;
			//}
		 //}
		 //loop++ ;
		 ////result = THERM_READ(); // get pin value
		 ////_delay_us(460); // wait for rest of 480uS period
	//}
	 //return SYS_FAIL;
 //}
/**************************************************************
  Function   :  ds1820_Reset
  Purpose    :  All communication with the DS18S20 begins with an
					 initialization sequence that consists of a reset 
					 pulse from the master followed by a presence pulse
					 from the DS18S20.
  Parameters : When the DS18S20 sends the presence pulse in response
					to the reset, it's indicating to the master that it
					is on the bus and ready to operate.
  Returns    : Presence pulse value (0 = presence valid)
  Notes      :  
 * During the initialization sequence the bus master transmits the
	reset pulse by pulling the 1-Wire bus low for a minimum of 480?s. 
 * The bus master then releases the bus and goes into receive mode. 
	When the bus is released, the pullup resistor pulls the 1-Wire bus high.
 * When the DS18S20 detects this rising edge, it waits 15?s to 60?s and 
	then transmits a presence pulse by pulling the 1-Wire bus low for 
	60?s to 240?s.
***************************************************************/
UI_8 ds1820_Reset(void)
{
	UI_8 result = 0 ;

	THERM_OUTPUT(); // set pin as output
	THERM_LOW(); // pull pin low for 480uS
	_delay_us(480);
	THERM_INPUT(); // set pin as input
	_delay_us(60); // wait for 60uS
	result = THERM_READ(); // get pin value
	_delay_us(460); // wait for rest of 480uS period
	
	return result;
}
/**************************************************************/
 UI_8 ds1820_ReadBit()
 {
	 UI_8 bit = 0;

	 THERM_OUTPUT();	// set pin as output
	 THERM_LOW();		// pull pin low for 1uS
	 _delay_us(1);
	 THERM_INPUT();	// release pin & wait 14 uS
	 _delay_us(14);
	 if (THERM_READ())
			 bit = 1;			// read pin value

	 _delay_us(45);		// wait rest of 60uS period
	 return bit;
 }
/**************************************************************/
 UI_8 ds1820_ReadByte(void)
 {
	 UI_8 i=8, data=0;

	 while(i--) // for 8 bits:
	 {
		 data >>= 1; // shift all bits right
		 data |= (ds1820_ReadBit() << 7); // get next bit (LSB first)
	 }
	 return data;
 }


 /*********************************************************************
 ds18B20_ReadTempC()
 ------------------
 Returns temperature in Celsius as a float value.
 Uses 11-bit resolution (0.125°C/bit)
 
 Note: Temperature is read using 11-bit resolution. The device is configured
 for 11-bit mode in ds1820_Initialise().
***********************************************************************/
void ds1820_ReadTempC(int16_t *temperature)  // Changed from int8_t to int16_t
{
    UI_8 t0 = 0, t1 = 0;
    UI_8 polarity = POSITIVE;
    UI_8 whole = 0;
    UI_8 decimal = 0;

    ds1820_ReadTempRaw(rom0, &t0, &t1);

    // Check if temperature is negative
    if (t1 & 0xF8) {
        polarity = NEGATIVE;
        // Handle negative temperature - invert bits and add 1
        t0 = ~t0;
        t1 = ~t1;
        if (++t0 == 0) ++t1;
    }

    whole = (t1 & 0x07) << 4;      // grab lower 3 bits of t1
    whole |= t0 >> 4;              // and upper 4 bits of t0
    decimal = t0 & 0x0F;           // decimals in lower 4 bits of t0
    decimal *= BIT_11;             // decimal now contains fractional part * 125
    decimal &= 0xFF;

    // Convert to temperature in tenths of degrees (1 decimal place)
    int16_t raw = (whole * 10) + (decimal * 10 / 1000);

    if (polarity == NEGATIVE) {
        raw = -raw;
    }

    *temperature = raw; // Return temperature in tenths of degrees (no casting needed)
}

/**************************************************************/
  // ROM READER PROGRAM
  // Read the ID of the attached Dallas 18B20 device
  // Note: only ONE device should be on the bus.
/**************************************************************/
 void RomReaderProgram()
 {
	UI_8 i;
	UI_8 data ;
	UI_8 crc8 = 0;

	// write 64-bit ROM code on first LCD line
	ds1820_Reset();
	ds1820_WriteByte(THERM_READROM);
	
	for (i=0; i<8; i++)
	{
		data = ds1820_ReadByte();
		rom0[i] = data ;
		#if 1
		console_writeline("ROM_%d: 0x%02x", i, data);
		#endif
	}
	/* Calculate CRC
	------------------*/	
	for (i = 0; i < 7; i++) 
	{
		crc8 = OWI_ComputeCRC8(rom0[i], crc8);
		#if 0
		console_writeline("\n\rCRC: %d", crc8);
		#endif		
	}
	if (crc8 == rom0[7]) 
	{
		console_writeline("ROM CRC passed");	
	}
	else
	{
		console_writeline("ROM CRC Failed");	
	}
 }

/********************************************************************
  Function   : ROM_Search
  Purpose    :
  Parameters : 
  Returns    : 
  Notes      :  presence = 0 => presence, 1 = no part
 
 Presence Pulses
 All communication with the DS18S20 begins with an initialization 
 sequence that consists of a reset pulse from the master followed 
 by a presence pulse from the DS18S20. 
 When the DS18S20 sends the presence pulse in response to the reset, 
 it is indicating to the master that it is on the bus and ready to operate.
 During the initialization sequence the bus master transmits the
 reset pulse by pulling the 1-Wire bus low for a minimum of 480?s. 
 The bus master then releases the bus and goes into receive mode. 
 When the bus is released, the pullup resistor pulls the 1-Wire bus high.
 When the DS18S20 detects this rising edge, it waits 15?s to 60?s and 
 then transmits a presence pulse by pulling the 1-Wire bus low for 
 60?s to 240?s.
*********************************************************************/
 void ROM_Search(void)
 {
	
 }
/*******************************************************/
// FIND DEVICES
void FindDevices(void)
{
	UI_8 ROM[8] ; // ROM Bit
	UI_8 lastDiscrep = 0; // last discrepancy
	UI_8 doneFlag = 0; // Done flag
	UI_8 FoundROM[8]; // table of found ROM codes
	UI_8 NumofROM[5];
	//UI_8 FoundROM[5][8]; // table of found ROM codes
	UI_8 numROMs;
	UI_8 m;

	if(!ds1820_Reset()) //Begins when a presence is detected
	{  
		//Begins when at least one part is found
		if(First(lastDiscrep, doneFlag, ROM)) 
		{			
			console_writeline("\n\t\t\tGot here_1");
			HALT
			numROMs=0;
			do
			{
				numROMs++;
				console_writeline("\n\rROM device# = %d", NumofROM[numROMs]) ;
				for(m=0; m<8; m++)
				{
				//Identifies ROM number of found device
					FoundROM[m] = ROM[m]; 
				} 
				//printf("\nROM CODE =%02X%02X%02X%02X\n",
				//FoundROM[5][7],FoundROM[5][6],FoundROM[5][5],FoundROM[5][4],
				//FoundROM[5][3],FoundROM[5][2],FoundROM[5][1],FoundROM[5][0]);
				//}while (Next()&&(numROMs<10));
				console_writeline("\n\rROM CODE = ") ;
				for (m = 7; m > 0; m--)
				{
					console_writeline(" 0x%02X", FoundROM[m]) ;
				}
			}
			while (Next(lastDiscrep, doneFlag, ROM)&&(numROMs<10)); //Continues until no additional devices are found
		}
	}
}
 // FIRST
 // The First function resets the current state of a ROM search and calls
 // Next to find the first device on the 1-Wire bus.
 //
 UI_8 First(UI_8 lastDiscrep, UI_8 doneFlag, UI_8 ROM[])
 {
	 lastDiscrep = 0; // reset the rom search last discrepancy global
	 doneFlag = FALSE;
	 return Next(lastDiscrep, doneFlag, ROM); // call Next and return its return value
 }

 // NEXT
 // The Next function searches for the next device on the 1-Wire bus. If
 // there are no more devices on the 1-Wire then false is returned.
 //
 UI_8 Next(UI_8 lastDiscrep, UI_8 doneFlag, UI_8 ROM[])
 {
	 UI_8 m = 1; // ROM Bit index
	 UI_8 n = 0; // ROM Byte index
	 UI_8 k = 1; // bit mask
	 UI_8 x = 0;
	 UI_8 discrepMarker = 0; // discrepancy marker
	 UI_8 g; // Output bit
	 UI_8 nxt; // return value
	 unsigned int flag;
	 UI_8 dowcrc = 0 ;
	 
	 nxt = FALSE ; // set the next flag to false
	 dowcrc = 0; // reset the dowcrc
	 flag = ds1820_Reset(); // reset the 1-Wire	
	 if(flag||doneFlag) // no parts -> return false
	 {
		 lastDiscrep = 0; // reset the search
		 return FALSE;
	 }	  
	 while (1)
	 {
	 	ds1820_WriteByte(0xF0); // send SearchROM command
		UI_16 ms = 5000 ;
		while (ms--)
		{
			uint16_t count = (F_CPU / 1000 / 4) ;
			while (count--)
			{
				asm volatile ("nop");
			}
		}
		//_delay_ms(5000);
	 }
	  console_writeline("\n\t\t\tGot here_1");
	  HALT
	 do
	 // for all eight bytes
	 {
		 x = 0;
		 //if(read_bit()==1)
		 if (ds1820_ReadBit() == 1)
				x = 2;
		 //delay(6);
		 _delay_us(120);
		 
		 //if(read_bit()==1) 
		 if (ds1820_ReadBit() == 1)
		 {
				x |= 1;			// and its complement				
		 }
		 if(x ==3)				// there are no devices on the 1-Wire
		 {				
				break;
		 }
		 else
		 {
			 console_writeline("\n\t\t\tGot here_2");
			 if(x>0) // all devices coupled have 0 or 1
					g = x>>1; // bit write value for search
			 else
			 {
				 // if this discrepancy is before the last
				 // discrepancy on a previous Next then pick
				 // the same as last time
				 if(m<lastDiscrep)
				 g = ((ROM[n]&k)>0);
				 else // if equal to last pick 1
				 g = (m==lastDiscrep); // if not then pick 0
				 // if 0 was picked then record
				 // position with mask k
				 if (g==0) discrepMarker = m;
				 console_writeline("\n\t\t\tGot here_3");
				 //HALT
			 }
			 if(g==1) // isolate bit in ROM[n] with mask k
			 ROM[n] |= k;
			 else
			 ROM[n] &= ~k;
			 ds1820_WriteBit(g); // ROM search write
			 m++; // increment bit counter m
			 k = k<<1; // and shift the bit mask k
			 if(k==0) // if the mask is 0 then go to new ROM
			 { // byte n and reset mask
				 ow_crc(ROM[n], dowcrc); // accumulate the CRC
				 n++; k++;
			 }
		 }
	 }while(n<8); //loop until through all ROM bytes 0-7
	 if(m<65||dowcrc) // if search was unsuccessful then
	 lastDiscrep=0; // reset the last discrepancy to 0
	 else
	 {
		 // search was successful, so set lastDiscrep,
		 // lastOne, nxt
		 lastDiscrep = discrepMarker;
		 doneFlag = (lastDiscrep==0);
		 nxt = TRUE; // indicates search is not complete yet, more
		 // parts remain
	 }
	 return nxt;
 }

 //////////////////////////////////////////////////////////////////////////////
 // ONE WIRE CRC
 //
 void ow_crc( UI_8 x, UI_8 dowcrc)
 {
	 dowcrc = dscrc_table[dowcrc^x];
	 //return dowcrc;
 }

/*******************************************************/
 UI_8 OWI_ComputeCRC8(UI_8 inData, UI_8 seed)
 {
	 UI_8 bitsLeft;
	 UI_8 temp;

	 for (bitsLeft = 8; bitsLeft > 0; bitsLeft--) 
	 {
		 temp = ((seed ^ inData) & 0x01);
		 if (temp == 0) 
		 {
			 seed >>= 1;
		 } 
		 else 
		 {
			 seed ^= 0x18;
			 seed >>= 1;
			 seed |= 0x80;
		 }
		 inData >>= 1;
	 }
	 return seed;
 }

/**********************************************************************
Only the DS18B20 that exactly matches the 64-bit ROM sequence
will respond to the following memory function command.
***********************************************************************/
void ds1820_MatchRom(UI_8 rom[])
{
	ds1820_WriteByte(THERM_MATCHROM);
	for (UI_8 i=0;i<8;i++)
		ds1820_WriteByte(rom[i]);
}
/*************************************************************
 ds1820_ReadTempRaw()
 -------------------
 Returns the two temperature bytes from the scratch-pad
**************************************************************/
void ds1820_ReadTempRaw(UI_8 id[], UI_8 *t0, UI_8 *t1)
{
	ds1820_Reset(); // skip ROM & start temp conversion
	if (id) ds1820_MatchRom(id);
	else ds1820_WriteByte(THERM_SKIPROM);
	ds1820_WriteByte(THERM_CONVERTTEMP);
	while (!ds1820_ReadBit()); // wait until conversion completed

	ds1820_Reset(); // read first two bytes from scratchpad
	if (id) ds1820_MatchRom(id);
	else ds1820_WriteByte(THERM_SKIPROM);
	ds1820_WriteByte(THERM_READSCRATCH);
	*t0 = ds1820_ReadByte(); // first UI_8
	*t1 = ds1820_ReadByte(); // second byte
}

/**************************************************************/
void ds1820_WriteBit(UI_8 bit)
{
	THERM_OUTPUT(); // set pin as output
	THERM_LOW(); // pull pin low for 1uS
	_delay_us(1);
	if (bit) THERM_INPUT(); // to write 1, float pin
	_delay_us(60);
	THERM_INPUT(); // wait 60uS & release pin
}
/**************************************************************/
void ds1820_WriteByte(UI_8 data)
{
	UI_8 i=8;
	while(i--) // for 8 bits:
	{
		ds1820_WriteBit(data&1); // send least significant bit
		data >>= 1; // shift all bits right
	}
}

