/*
 * OW_DS1820.c
 *
 * Created: 2020/04/30 12:48:14 PM
 * Amended 2021/2/26
 *  Author: <PERSON><PERSON><PERSON>
 */ 
 // ---------------------------------------------------------------------------
 // "ONE-WIRE" ROUTINES
 //
 #include <avr/io.h>
 #include <stdlib.h>
 #include <stdio.h>
 #include <string.h>
 #include <avr/pgmspace.h>
 #include <math.h>
 #include "console.h"
 #include "types.h"
 #include "system.h" 
 #include "delay.h"
 #include "io_pins.h"
 #include <util/delay.h> // used for _delay_us function
 #include "temperature.h"

 #define THERM_PORT PORTB
 #define THERM_DDR DDRB
 #define THERM_PIN PINB
 #define THERM_IO 4
 #define THERM_INPUT() ClearBit(THERM_DDR,THERM_IO) // make pin an input
 #define THERM_OUTPUT() SetBit(THERM_DDR,THERM_IO) // make pin an output
 #define THERM_LOW() ClearBit(THERM_PORT,THERM_IO) // take (output) pin low
 #define THERM_HIGH() SetBit(THERM_PORT,THERM_IO) // take (output) pin high
 #define THERM_READ() ReadBit(THERM_PIN,THERM_IO) // get (input) pin value
 #define THERM_CONVERTTEMP 0x44
 #define THERM_READSCRATCH 0xBE
 #define THERM_WRITESCRATCH 0x4E
 #define THERM_COPYSCRATCH 0x48
 #define THERM_READPOWER 0xB4
 #define THERM_SEARCHROM 0xF0
 #define THERM_READROM 0x33
 #define THERM_MATCHROM 0x55
 #define THERM_SKIPROM 0xCC
 #define THERM_ALARMSEARCH 0xEC
 #define CONV_9BIT	0x1F
 #define CONV_10BIT	0x3F
 #define CONV_11BIT	0x5F
 #define CONV_12BIT	0x7F
 #define BIT_9		5
 #define BIT_10	25
 #define BIT_11	125
 #define BIT_12	625

 #define FALSE 0
 #define TRUE 1
 #define NEGATIVE 0
 #define POSITIVE 1

 #define ClearBit(x,y) x &= ~_BV(y) // equivalent to cbi(x,y)
 #define SetBit(x,y) x |= _BV(y)			// equivalent to sbi(x,y)
 #define ReadBit(x,y) x & _BV(y)		// call with PINx and bit#

 //#define DQ_RD PinRD(PIND5, PIND) 
 //#define DQ_WR_1 PinWR(THERM_IO, &THERM_PORT, 1)
 //#define DQ_WR_0 PinWR(THERM_IO, &THERM_PORT, 0)

 typedef uint8_t byte; // I just like byte & sbyte better
 typedef int8_t sbyte;
 /* Prototypes
 --------------*/
 uint8_t ds1820_Reset();
 uint8_t ds1820_ReadBit();
 uint8_t ds1820_ReadByte();
 void RomReaderProgram();
 void ds1820_ReadTempC(int16_t *temperature); // Changed from int8_t to int16_t
 void ds1820_MatchRom(uint8_t rom[]);
 void ds1820_ReadTempRaw(uint8_t id[], uint8_t *t0, uint8_t *t1);
 void ds1820_WriteBit(uint8_t bit);
 void ds1820_WriteByte(uint8_t data);
 void ds1820_initialise(void) ;
 void ROM_Search(void) ;
 //void DS1820_Test(void);
 uint8_t OWI_ComputeCRC8(uint8_t inData, uint8_t seed) ;

 /* uint8_t read_bit(void);
  void write_bit(uint8_t bitval); */
  uint8_t read_byte(void);
  void write_byte(uint8_t val);
  void Read_Temperature(void);
  uint8_t ow_reset(void);
  uint8_t First(uint8_t lastDiscrep, uint8_t doneFlag, uint8_t ROM[]) ;
  uint8_t Next(uint8_t lastDiscrep, uint8_t doneFlag, uint8_t ROM[]) ;
  void ow_crc( uint8_t x, uint8_t dowcrc) ;

 // the following arrays specify the addresses of *my* ds18b20 devices
 // substitute the address of your devices before using.
 //uint8_t rom0[] = {0x28, 0xFF, 0x61, 0x0F, 0xA0, 0x16, 0x04, 0xDF};
 //uint8_t rom0[] = {0x28, 0xFF, 0xE3, 0x64, 0xA0, 0x16, 0x05, 0xCD} ;
 uint8_t rom0[10] ;
 const uint8_t dscrc_table[] PROGMEM = {
	 0, 94,188,226, 97, 63,221,131,194,156,126, 32,163,253, 31, 65,
	 157,195, 33,127,252,162, 64, 30, 95, 1,227,189, 62, 96,130,220,
	 35,125,159,193, 66, 28,254,160,225,191, 93, 3,128,222, 60, 98,
	 190,224, 2, 92,223,129, 99, 61,124, 34,192,158, 29, 67,161,255,
	 70, 24,250,164, 39,121,155,197,132,218, 56,102,229,187, 89, 7,
	 219,133,103, 57,186,228, 6, 88, 25, 71,165,251,120, 38,196,154,
	 101, 59,217,135, 4, 90,184,230,167,249, 27, 69,198,152,122, 36,
	 248,166, 68, 26,153,199, 37,123, 58,100,134,216, 91, 5,231,185,
	 140,210, 48,110,237,179, 81, 15, 78, 16,242,172, 47,113,147,205,
	 17, 79,173,243,112, 46,204,146,211,141,111, 49,178,236, 14, 80,
	 175,241, 19, 77,206,144,114, 44,109, 51,209,143, 12, 82,176,238,
	 50,108,142,208, 83, 13,239,177,240,174, 76, 18,145,207, 45,115,
	 202,148,118, 40,171,245, 23, 73, 8, 86,180,234,105, 55,213,139,
	 87, 9,235,181, 54,104,138,212,149,203, 41,119,244,170, 72, 22,
	 233,183, 85, 11,136,214, 52,106, 43,117,151,201, 74, 20,246,168,
 116, 42,200,150, 21, 75,169,247,182,232, 10, 84,215,137,107, 53};
 
/*************************************************************
NOTE: For temperature decimal resolution the following are defined:-
 #define CONV_9BIT 0x1F - 0.5 �C
 #define CONV_10BIT 0x3F - 0.25 �C
 #define CONV_11BIT 0x5F - 0.125 �C
 #define CONV_12BIT 0x7F - 0.0625 �C
  
Currently the configuration is for 12 bit resolution.
This provides the highest precision temperature readings (0.0625°C resolution).

Temperature calculation uses BIT_12 (625) for fractional resolution.
*************************************************************
 ds1820_Initialise()
 -------------------
 Write to byte 4 in scratch-pad area to set device for 12 bit 
 resolution. Note bit msb15 is the Sign bit (& with 0x80)
 NOTE: Configured for 12bits resolution!
**************************************************************/
void ds1820_initialise(void)
{
	uint8_t i = 0 ;
	uint8_t RD_byte[10] ;
	
	console_writeline("DS1820 Initialisation (12-bit mode):");
	/* Skip ROM & write to scratchpad  
	------------------------------------------*/
	ds1820_Reset();
	ds1820_WriteByte(THERM_SKIPROM);
	ds1820_WriteByte(THERM_WRITESCRATCH);

	/* Starts write from byte nr. 2, 0 & 1 are read only */
	ds1820_WriteByte(0x00);				/* byte 2 - TH register */
	ds1820_WriteByte(0x00);				/* byte 3 - TL register */
	ds1820_WriteByte(CONV_12BIT);		/* byte 4 - Config register */
	
	console_writeline("Written config: TH=0x00, TL=0x00, Config=0x%02X", CONV_12BIT);
	
	// Add delay to ensure write completes
	delay_ms(10);

	/* Verify write to scratch pad memory */	
	ds1820_Reset();
	ds1820_WriteByte(THERM_SKIPROM);
	ds1820_WriteByte(THERM_READSCRATCH);
	
	for (i = 0; i < 9; i++)
	{
		RD_byte[i] = ds1820_ReadByte(); 
	}

	/*
	The DS1820 scratchpad memory layout is:
		Byte 0: Temperature LSB
		Byte 1: Temperature MSB
		Byte 2: TH (high alarm trigger)
		Byte 3: TL (low alarm trigger)
		Byte 4: Configuration register (this is what we're verifying)
		Byte 5: Reserved (0xFF)
		Byte 6: Reserved (for DS18S20, count remaining)
		Byte 7: Reserved (for DS18S20, count per °C)
		Byte 8: CRC
	*/
	
	console_write("Scratch memory content: ");
	for (i = 0; i < 9; i++)
	{
		console_write("%02X ", RD_byte[i]);
	}
	console_writeline("");
	
	if (RD_byte[4] == CONV_12BIT)
	{
		console_writeline("Config verify passed - 12-bit mode set (0.0625°C resolution).");
		
		// Copy scratchpad to EEPROM to make configuration permanent
		ds1820_Reset();
		ds1820_WriteByte(THERM_SKIPROM);
		ds1820_WriteByte(THERM_COPYSCRATCH);
		delay_ms(10); // Wait for copy to complete
		console_writeline("12-bit configuration copied to EEPROM.");
	}
	else
	{
		console_writeline("Config verify failed - expected 0x%02X, got 0x%02X", CONV_12BIT, RD_byte[4]);
		console_writeline("Attempting to continue with device's current configuration...");
	}	
}
/**************************************************************/ 
 //uint8_t _ds1820_Reset(void)
 //{
	 //uint8_t result = 0 ;
	 //uint8_t loop = 0 ;
//
	//while (loop < 100)
	//{
		 //THERM_OUTPUT(); // set pin as output
		 //THERM_LOW(); // pull pin low for 480uS
		 //_delay_us(480);
		 //THERM_INPUT(); // set pin as input
		 ////_delay_us(60); // wait for 60uS
		 //if (!(THERM_READ()))
		 //{
			//return true;
		 //}
		 //while (THERM_READ())
		 //{
			//result ++ ;
			//if (result > 60)
			//{
				//break ;
			//}
		 //}
		 //loop++ ;
		 ////result = THERM_READ(); // get pin value
		 ////_delay_us(460); // wait for rest of 480uS period
	//}
	 //return false;
 //}
/**************************************************************
  Function   :  ds1820_Reset
  Purpose    :  All communication with the DS18S20 begins with an
					 initialization sequence that consists of a reset 
					 pulse from the master followed by a presence pulse
					 from the DS18S20.
  Parameters : When the DS18S20 sends the presence pulse in response
					to the reset, it's indicating to the master that it
					is on the bus and ready to operate.
  Returns    : Presence pulse value (0 = presence valid)
  Notes      :  
 * During the initialization sequence the bus master transmits the
	reset pulse by pulling the 1-Wire bus low for a minimum of 480?s. 
 * The bus master then releases the bus and goes into receive mode. 
	When the bus is released, the pullup resistor pulls the 1-Wire bus high.
 * When the DS18S20 detects this rising edge, it waits 15?s to 60?s and 
	then transmits a presence pulse by pulling the 1-Wire bus low for 
	60?s to 240?s.
***************************************************************/
uint8_t ds1820_Reset(void)
{
	uint8_t result = 0 ;

	THERM_OUTPUT(); // set pin as output
	THERM_LOW(); // pull pin low for 480uS
	_delay_us(480);
	THERM_INPUT(); // set pin as input
	THERM_HIGH(); // enable internal pullup
	_delay_us(60); // wait for 60uS
	result = THERM_READ(); // get pin value
	_delay_us(460); // wait for rest of 480uS period
	
	return result;
}
/**************************************************************/
 uint8_t ds1820_ReadBit()
 {
	 uint8_t bit = 0;

	 // Disable interrupts during timing-critical section
	 uint8_t sreg = SREG;
	 cli();
	 
	 THERM_OUTPUT();	// set pin as output
	 THERM_LOW();		// pull pin low for 3uS
	 _delay_us(3);
	 THERM_INPUT();		// release pin
	 THERM_HIGH();		// enable internal pullup
	 _delay_us(12);		// wait before reading
	 bit = THERM_READ() ? 1 : 0;	// read pin value
	 
	 // Restore interrupts
	 SREG = sreg;
	 
	 _delay_us(50);		// wait rest of slot
	 return bit;
 }
/**************************************************************/
 uint8_t ds1820_ReadByte(void)
 {
	 uint8_t i=8, data=0;
	 uint8_t bit;

	 while(i--) // for 8 bits:
	 {
		 data >>= 1; // shift all bits right
		 bit = ds1820_ReadBit();
		 data |= (bit << 7); // get next bit (LSB first)
	 }
	 return data;
 }


 /*********************************************************************
 ds18B20_ReadTempC()
 ------------------
 Returns temperature in Celsius as a fixed-point value (tenths of degrees).
 Uses 12-bit resolution providing 0.0625°C precision.
 
 Temperature is returned in tenths of degrees for single decimal place display.
***********************************************************************/
void ds1820_ReadTempC(int16_t *temperature)  // Changed from int8_t to int16_t
{
    uint8_t t0 = 0, t1 = 0;
    uint8_t polarity = POSITIVE;
    uint8_t whole = 0;
    uint16_t decimal = 0;

	/*
	DS18B20 Temperature Data Format (12-bit mode):
	
	t1 (MSB)     t0 (LSB)
	SSSSIIII     FFFF0000
	
	Where:
	S = Sign bits (bits 15-12) - in two's complement format
	I = Integer temperature bits (bits 11-4) 
	F = Fractional bits (bits 3-0) - all 4 bits used in 12-bit mode
	
	Note: 0xF8 mask checks bits 7-3 of t1 (bits 15-11 of full word)
	      to detect negative temperatures in two's complement format
	*/
    ds1820_ReadTempRaw(rom0, &t0, &t1);

    // Check if temperature is negative
    if (t1 & 0xF8) {
        polarity = NEGATIVE;
        // Handle negative temperature - invert bits and add 1
        t0 = ~t0;
        t1 = ~t1;
        if (++t0 == 0) ++t1;
    }

    whole = (t1 & 0x07) << 4;      // grab lower 3 bits of t1
    whole |= t0 >> 4;              // and upper 4 bits of t0
    decimal = t0 & 0x0F;           // decimals in lower 4 bits of t0
    
    decimal *= BIT_12;   // decimal now contains fractional part * 625
    
    // Convert to temperature in tenths of degrees (1 decimal place)
    int16_t raw = (whole * 10) + (decimal * 10 / 10000);

    if (polarity == NEGATIVE) {
        raw = -raw;
    }

    *temperature = raw; // Return temperature in tenths of degrees
}

/**************************************************************/
  // ROM READER PROGRAM
  // Read the ID of the attached Dallas 18B20 device
  // Note: only ONE device should be on the bus.
/**************************************************************/
 void RomReaderProgram()
 {
	uint8_t i;
	uint8_t data ;
	uint8_t crc8 = 0;

	uint8_t result = ds1820_Reset();
	if (result) {
		console_writeline("No DS1820 device found on the bus.");
		return;
	}

	// Add small delay after reset before sending command
	_delay_ms(1);
	
	ds1820_WriteByte(THERM_READROM);
	
	console_write("Reading 8 ROM bytes: ");
	for (i=0; i<8; i++)
	{
		data = ds1820_ReadByte();
		rom0[i] = data ;
		console_write("0x%02X ", data);
	}

	/* Calculate CRC
	------------------*/
	for (i = 0; i < 7; i++) 
	{
		crc8 = OWI_ComputeCRC8(rom0[i], crc8);
	}

	if (crc8 == rom0[7]) 
	{
		console_writeline("CRC passed");	
	}
	else
	{
		console_writeline("CRC failed");	
		console_writeline("  Calculated: 0x%02X, expected: 0x%02X", crc8, rom0[7]);	
	}
 }

/********************************************************************
  Function   : ROM_Search
  Purpose    :
  Parameters : 
  Returns    : 
  Notes      :  presence = 0 => presence, 1 = no part
 
 Presence Pulses
 All communication with the DS18S20 begins with an initialization 
 sequence that consists of a reset pulse from the master followed 
 by a presence pulse from the DS18S20. 
 When the DS18S20 sends the presence pulse in response to the reset, 
 it is indicating to the master that it is on the bus and ready to operate.
 During the initialization sequence the bus master transmits the
 reset pulse by pulling the 1-Wire bus low for a minimum of 480?s. 
 The bus master then releases the bus and goes into receive mode. 
 When the bus is released, the pullup resistor pulls the 1-Wire bus high.
 When the DS18S20 detects this rising edge, it waits 15?s to 60?s and 
 then transmits a presence pulse by pulling the 1-Wire bus low for 
 60?s to 240?s.
*********************************************************************/
 void ROM_Search(void)
 {
	
 }
/*******************************************************/
// FIND DEVICES
void FindDevices(void)
{
	uint8_t ROM[8] ; // ROM Bit
	uint8_t lastDiscrep = 0; // last discrepancy
	uint8_t doneFlag = 0; // Done flag
	uint8_t FoundROM[8]; // table of found ROM codes
	uint8_t NumofROM[5];
	//uint8_t FoundROM[5][8]; // table of found ROM codes
	uint8_t numROMs;
	uint8_t m;

	if(!ds1820_Reset()) //Begins when a presence is detected
	{  
		//Begins when at least one part is found
		if(First(lastDiscrep, doneFlag, ROM)) 
		{			
			console_writeline("\n\t\t\tGot here_1");
			HALT
			numROMs=0;
			do
			{
				numROMs++;
				console_writeline("\n\rROM device# = %d", NumofROM[numROMs]) ;
				for(m=0; m<8; m++)
				{
				//Identifies ROM number of found device
					FoundROM[m] = ROM[m]; 
				} 
				//printf("\nROM CODE =%02X%02X%02X%02X\n",
				//FoundROM[5][7],FoundROM[5][6],FoundROM[5][5],FoundROM[5][4],
				//FoundROM[5][3],FoundROM[5][2],FoundROM[5][1],FoundROM[5][0]);
				//}while (Next()&&(numROMs<10));
				console_writeline("\n\rROM CODE = ") ;
				for (m = 7; m > 0; m--)
				{
					console_writeline(" 0x%02X", FoundROM[m]) ;
				}
			}
			while (Next(lastDiscrep, doneFlag, ROM)&&(numROMs<10)); //Continues until no additional devices are found
		}
	}
}
 // FIRST
 // The First function resets the current state of a ROM search and calls
 // Next to find the first device on the 1-Wire bus.
 //
 uint8_t First(uint8_t lastDiscrep, uint8_t doneFlag, uint8_t ROM[])
 {
	 lastDiscrep = 0; // reset the rom search last discrepancy global
	 doneFlag = FALSE;
	 return Next(lastDiscrep, doneFlag, ROM); // call Next and return its return value
 }

 // NEXT
 // The Next function searches for the next device on the 1-Wire bus. If
 // there are no more devices on the 1-Wire then false is returned.
 //
 uint8_t Next(uint8_t lastDiscrep, uint8_t doneFlag, uint8_t ROM[])
 {
	 uint8_t m = 1; // ROM Bit index
	 uint8_t n = 0; // ROM Byte index
	 uint8_t k = 1; // bit mask
	 uint8_t x = 0;
	 uint8_t discrepMarker = 0; // discrepancy marker
	 uint8_t g; // Output bit
	 uint8_t nxt; // return value
	 unsigned int flag;
	 uint8_t dowcrc = 0 ;
	 
	 nxt = FALSE ; // set the next flag to false
	 dowcrc = 0; // reset the dowcrc
	 flag = ds1820_Reset(); // reset the 1-Wire	
	 if(flag||doneFlag) // no parts -> return false
	 {
		 lastDiscrep = 0; // reset the search
		 return FALSE;
	 }	  
	 while (1)
	 {
	 	ds1820_WriteByte(0xF0); // send SearchROM command
		uint16_t ms = 5000 ;
		while (ms--)
		{
			uint16_t count = (F_CPU / 1000 / 4) ;
			while (count--)
			{
				asm volatile ("nop");
			}
		}
		//_delay_ms(5000);
	 }
	  console_writeline("\n\t\t\tGot here_1");
	  HALT
	 do
	 // for all eight bytes
	 {
		 x = 0;
		 //if(read_bit()==1)
		 if (ds1820_ReadBit() == 1)
				x = 2;
		 //delay(6);
		 _delay_us(120);
		 
		 //if(read_bit()==1) 
		 if (ds1820_ReadBit() == 1)
		 {
				x |= 1;			// and its complement				
		 }
		 if(x ==3)				// there are no devices on the 1-Wire
		 {				
				break;
		 }
		 else
		 {
			 console_writeline("\n\t\t\tGot here_2");
			 if(x>0) // all devices coupled have 0 or 1
					g = x>>1; // bit write value for search
			 else
			 {
				 // if this discrepancy is before the last
				 // discrepancy on a previous Next then pick
				 // the same as last time
				 if(m<lastDiscrep)
				 g = ((ROM[n]&k)>0);
				 else // if equal to last pick 1
				 g = (m==lastDiscrep); // if not then pick 0
				 // if 0 was picked then record
				 // position with mask k
				 if (g==0) discrepMarker = m;
				 console_writeline("\n\t\t\tGot here_3");
				 //HALT
			 }
			 if(g==1) // isolate bit in ROM[n] with mask k
			 ROM[n] |= k;
			 else
			 ROM[n] &= ~k;
			 ds1820_WriteBit(g); // ROM search write
			 m++; // increment bit counter m
			 k = k<<1; // and shift the bit mask k
			 if(k==0) // if the mask is 0 then go to new ROM
			 { // byte n and reset mask
				 ow_crc(ROM[n], dowcrc); // accumulate the CRC
				 n++; k++;
			 }
		 }
	 }while(n<8); //loop until through all ROM bytes 0-7
	 if(m<65||dowcrc) // if search was unsuccessful then
	 lastDiscrep=0; // reset the last discrepancy to 0
	 else
	 {
		 // search was successful, so set lastDiscrep,
		 // lastOne, nxt
		 lastDiscrep = discrepMarker;
		 doneFlag = (lastDiscrep==0);
		 nxt = TRUE; // indicates search is not complete yet, more
		 // parts remain
	 }
	 return nxt;
 }

 //////////////////////////////////////////////////////////////////////////////
 // ONE WIRE CRC
 //
 void ow_crc( uint8_t x, uint8_t dowcrc)
 {
	 dowcrc = dscrc_table[dowcrc^x];
	 //return dowcrc;
 }

/*******************************************************/
 uint8_t OWI_ComputeCRC8(uint8_t inData, uint8_t seed)
 {
	 uint8_t bitsLeft;
	 uint8_t temp;

	 for (bitsLeft = 8; bitsLeft > 0; bitsLeft--) 
	 {
		 temp = ((seed ^ inData) & 0x01);
		 if (temp == 0) 
		 {
			 seed >>= 1;
		 } 
		 else 
		 {
			 seed ^= 0x18;
			 seed >>= 1;
			 seed |= 0x80;
		 }
		 inData >>= 1;
	 }
	 return seed;
 }

/**********************************************************************
Only the DS18B20 that exactly matches the 64-bit ROM sequence
will respond to the following memory function command.
***********************************************************************/
void ds1820_MatchRom(uint8_t rom[])
{
	ds1820_WriteByte(THERM_MATCHROM);
	for (uint8_t i=0;i<8;i++)
		ds1820_WriteByte(rom[i]);
}
/*************************************************************
 ds1820_ReadTempRaw()
 -------------------
 Returns the two temperature bytes from the scratch-pad
**************************************************************/
void ds1820_ReadTempRaw(uint8_t id[], uint8_t *t0, uint8_t *t1)
{
	ds1820_Reset(); // skip ROM & start temp conversion
	if (id) ds1820_MatchRom(id);
	else ds1820_WriteByte(THERM_SKIPROM);
	ds1820_WriteByte(THERM_CONVERTTEMP);
	while (!ds1820_ReadBit()); // wait until conversion completed

	ds1820_Reset(); // read first two bytes from scratchpad
	if (id) ds1820_MatchRom(id);
	else ds1820_WriteByte(THERM_SKIPROM);
	ds1820_WriteByte(THERM_READSCRATCH);
	*t0 = ds1820_ReadByte(); // first uint8_t
	*t1 = ds1820_ReadByte(); // second byte
}

/**************************************************************/
void ds1820_WriteBit(uint8_t bit)
{
	THERM_OUTPUT(); // set pin as output
	THERM_LOW(); // pull pin low initially
	
	if (bit) {
		// Write 1: short low pulse (1-15us), then float high
		_delay_us(6);
		THERM_INPUT(); // float pin (pullup will pull high)
		THERM_HIGH();  // enable pullup
		_delay_us(64);
	} else {
		// Write 0: long low pulse (60-120us)
		_delay_us(60);
		THERM_INPUT(); // release pin
		THERM_HIGH();  // enable pullup
		_delay_us(10);
	}
}
/**************************************************************/
void ds1820_WriteByte(uint8_t data)
{
	uint8_t i=8;
	while(i--) // for 8 bits:
	{
		ds1820_WriteBit(data & 1); // send least significant bit
		data >>= 1; // shift all bits right
	}
}

