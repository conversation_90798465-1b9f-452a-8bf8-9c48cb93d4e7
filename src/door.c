#include "io_pins.h"
#include "system.h"

#include "door.h"

static int s_door_polatility = NORM_CLOSE;

void door_init(void)
{
    /* ------------------------------------------------------
    If DDRxn is written to '0', the Pin is configured as 
    an input 

    If PORTxn is written to '1' when the pin is configured
    as an input pin, the pull-up is activated 
    ---------------------------------------------------------*/
	DDRB &=  ~(1 << DOOR_SENSOR);
	PORTB = (1 << DOOR_SENSOR);	    // Activate pull-up

	PinWR(DOORALRM, &PORTB, 1) ;	// Write 1 to switch Door alarm off
}

inline void door_set_polarity(int polarity)
{
    s_door_polatility = polarity;
}

bool door_is_open(void)
{
    int pin_state = PinRD(DOOR_SENSOR, DOOR_PORT);

    if (s_door_polatility == NORM_CLOSE)
        return pin_state == 0;
    else
        return pin_state == 1;
}