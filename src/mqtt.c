/*
 * Mqtt_send.c
 *
 * Created: 2020/01/04 11:42:58 AM
 *  Author: <PERSON><PERSON><PERSON>
 */ 
 #include <string.h>
 #include <stdio.h>
 #include <stdlib.h>
 #include <avr/pgmspace.h>
 
 #include "types.h"
 #include "system.h"
 #include "lcd.h"
 #include "delay.h"
 #include "usart.h"
 #include "io_pins.h"
 #include "timer.h"
 #include "interrupts.h"
 #include "e2prom.h"
 #include "modem.h" 
 #include "mqtt.h"	
 #include "console.h"
 #include "config.h"
 #include "temperature.h"
 #include "power.h"
 #include "door.h"
 #include "state.h"
 #include "trigger.h"


 #define MQTT_PROTOCOL_LEVEL	3

 #define MQTT_CTRL_CONNECT		0x1
 #define MQTT_CTRL_CONNECTACK	0x2
 #define MQTT_CTRL_PUBLISH		0x3
 #define MQTT_CTRL_PUBACK		0x4
 #define MQTT_CTRL_PUBREC		0x5
 #define MQTT_CTRL_PUBREL		0x6
 #define MQTT_CTRL_PUBCOMP		0x7
 #define MQTT_CTRL_SUBSCRIBE	0x8
 #define MQTT_CTRL_SUBACK		0x9
 #define MQTT_CTRL_UNSUBSCRIBE	0xA
 #define MQTT_CTRL_UNSUBACK		0xB
 #define MQTT_CTRL_PINGREQ		0xC
 #define MQTT_CTRL_PINGRESP		0xD
 #define MQTT_CTRL_DISCONNECT	0xE

 #define MQTT_QOS_1				0x1
 #define MQTT_QOS_0				0x0

 /* Adjust as necessary, in seconds */
 #define MQTT_CONN_KEEPALIVE		60

 #define MQTT_CONN_USERNAMEFLAG	0x80
 #define MQTT_CONN_PASSWORDFLAG	0x40
 #define MQTT_CONN_WILLRETAIN		0x20
 #define MQTT_CONN_WILLQOS_1		0x08
 #define MQTT_CONN_WILLQOS_2		0x18
 #define MQTT_CONN_WILLFLAG		0x04
 #define MQTT_CONN_CLEANSESSION	0x02

 #define DEFAULT_BUFFER_SIZE		200
 #define DEFAULT_TIMEOUT			10000
 #define DEFAULT_CRLF_COUNT		2

 /* Select Demo */
 //#define SUBSRCIBE_DEMO								/* Define SUBSRCIBE demo */
 #define PUBLISH_TXDEVDATA								/* Define PUBLISH demo */

 //#define AIO_SERVER			"io.adafruit.com"		/* Adafruit server */
 #define AIO_SERVER			"broker.txdevsystems.com"
 //#define AIO_SERVER			"broker.hivemq.com"
 #define AIO_SERVER_PORT		"1883"				/* Server port */
 #define AIO_BASE_URL		"/api/v2"				/* Base URL for api */
 #define AIO_USERNAME	""		/* Enter username here */
 #define AIO_KEY			""		/* Enter AIO key here */
 #define AIO_FEED			""		/* Enter feed key */

 //#define APN				"internet"
 #define APN				"Justworx"
 #define USERNAME			""
 #define PASSWORD			""
 #define TOPIC_LEN		16
 #define HEADER_LNGTH	34		/* including \0 */
 /************************************************************************/ 

/*-----------------------------------------------------------
  Function   : MQTT_Init
  Purpose    : Initialises MQTT protocol.
  Parameters : 
  Returns    : Error code.
  Notes      : 
------------------------------------------------------------*/ 
 UI_8 MQTT_Init_Connect(void)
 {	
	if (USART0_SendCommand("AT+SAPBR=3,1,\"CONTYPE\",\"GPRS\"", "OK") == SYS_FAIL)
		return SYS_FAIL;

	if (USART0_SendCommand("AT+SAPBR=3,1,\"APN\",\"gigsky-02\"", "OK") == SYS_FAIL)
		return SYS_FAIL;

	if (USART0_SendCommand("AT+SAPBR=1,1", "OK") == SYS_FAIL)
		return SYS_FAIL;

	if (USART0_SendCommand("AT+SAPBR=2,1", "OK") == SYS_FAIL)
		return SYS_FAIL;

	// if (USART0_SendCommand("AT+SMCONF=\"URL\",\"broker.txdevsystems.co.za\"", "OK") == SYS_FAIL)
	if (USART0_SendCommand("AT+SMCONF=\"URL\",\"api.txdevsystems.co.za\"", "OK") == SYS_FAIL)
		return SYS_FAIL;

	if (USART0_SendCommand("AT+SMCONF=\"CLEANSS\",1", "OK") == SYS_FAIL)
		return SYS_FAIL;

	if (USART0_SendCommand("AT+SMCONN", "OK") == SYS_FAIL)
		return SYS_FAIL;

	return SYS_PASS;
 }
  /*-------------------------------------------------------------------------
  Function   : MQTT_Subscribe_RetMsg
  Purpose    : Initialises MQTT protocol for a return message to be received.
  Parameters : Sends " C<IMEI>,1 "
  Returns    : Error code.
  Notes      : Return message format:
	e.g.	 +SMPUBLISH: 1,"C869688057596290",11,"Brom;20;-20"
----------------------------------------------------------------------------*/ 
UI_8 MQTT_Subscribe_RetMsg(mqtt_data_struct *mqtt_dataptr)
{
	//UI_8 i = 0 ;
	/* SUBSCRIBE: 
	------------*/
	// for (i = 0; i < IMEI_LNGTH; i++)
	// {
		// mqtt_dataptr->IMEI[i] = EEPROM_RD_Char(IMEI_EEPROM + i) ;
		//USART1_Send_char(EEPROM_RD_Char(IMEI_EEPROM + i)) ;
	// }
	// memset(mqtt_dataptr->IMEI+15, '\0', sizeof(char));
	USART0_SendString("AT+SMSUB=\"C");
	USART0_SendString(mqtt_dataptr->IMEI);
	USART0_SendString("\"");
	USART0_SendString(",1\r\n\n");

	if (Wait_for_OK() ==  SYS_FAIL )
	{
		#ifdef _DEBUG_
		console_writeline("\nMqtt SMCONN failed\n\r");
		#endif
		return SYS_FAIL ;
	}
	//delay_ms_1(1000) ;
	return SYS_PASS ;
 }
 /*-----------------------------------------------------------
  Function   : MQTT_Init
  Purpose    : Initialises MQTT protocol.
  Parameters : 
  Returns    : Error code.
  Notes      : 
------------------------------------------------------------*/ 
UI_8 MQTT_Subscribe(void)
 {
	/* SUBSCRIBE: 
	------------*/
	USART0_SendString("AT+SMSUB=\"CR\",1\r\n\n");
	if (Wait_for_OK() ==  SYS_FAIL )
	{
		#ifdef _DEBUG_
		console_writeline("\nMqtt SMCONN failed\n\r");
		#endif
		return SYS_FAIL ;
	}
	delay_ms_1(1000) ;
	return SYS_PASS ;
 }

UI_8 MQTT_SubscribeTopic(char *topic)
{
	char cmd[64];

    snprintf(cmd, sizeof(cmd), "AT+SMSUB=\"%s\",1", topic);
	return USART0_SendCommand(cmd, "OK");
 }
/*-----------------------------------------------------------
  Function   : MQTT_Init
  Purpose    : Initialises MQTT protocol.
  Parameters : 
  Returns    : Error code.
  Notes      : 
------------------------------------------------------------*/ 
 void MQTT_Unsubscribe(void)
 {
	/* UNSUBSCRIBE: 
	---------------- */
	USART0_SendString("\n\n\rAT+SMUNSUB=\"CR\"\r\n");
	if (Wait_for_OK() ==  SYS_FAIL )
	{
		#ifdef _DEBUG_
		console_writeline("\nMqtt UN-Subscribe failed\n\r");
		#endif
	}	
 }

/*
* TOPIC: txdev/cr/c1
* DATA:
*/
bool ConfigPublish(void)
{
	char command[256] = "";

	snprintf(command, sizeof(command),"%s%s", 
		"AT+SMPUB=\"txdev/cr/c1\",1,0,",
		config_get_string()
	);

	return USART0_SendCommand(command, "OK") == SYS_PASS;
}

/*
* TOPIC: txdev/cr/d1
* DATA:
*   - imei: string
*   - temp_set_max: string
*   - current_temperature: string
*   - temp_set_min: string
*   - door_status: 0/1
*   - psu_status: 0/1
*   - supply_volt: string
*   - battery_low_trigger: 0/1
*   - battery_volt: string
*   - location: string
*   - signal_strength: string
*/
bool DataPublish(void)
{
	char command[256] = "";

	snprintf(command, sizeof(command),"%s%s:%s:%s:%s:%d:%d:%s:%d:%s:%s:%d:", 
		"AT+SMPUB=\"txdev/cr/d1\",1,0,",
		config_get_imei(),
		config_get_temp_max_string(),
		temperature_get_string(),
		config_get_temp_min_string(),
		door_is_open() ? 1 : 0,
		trigger_is_active(TRIGGER_MAINS) ? 0 : 1,
		power_get_mains_string(),
		trigger_is_active(TRIGGER_BATTERY) ? 0 : 1,
		power_get_battery_string(),
		"",
		state_get_current()->signal_strength
	);

	return USART0_SendCommand(command, "OK") == SYS_PASS;
}

/*
* TOPIC: txdev/cr/t1
* DATA:
*   - imei: string
*   - current_temperature: string
*   - unit_id: string
*   - supply_volt: string
*   - temp_set_max: string
*   - temp_set_min: string
*   - psu_trigger: 0/1
*   - door_trigger: 0/1
*   - battery_low_trigger: 0/1
*   - temperature_trigger: 0/1
*/
bool TriggerPublish(void)
{
	char command[256] = "";

	snprintf(command, sizeof(command),"%s%s:%s:%s:%s:%s:%s:%d:%d:%d:%d:", 
		"AT+SMPUB=\"txdev/cr/t1\",1,0,",
		config_get_imei(),
		temperature_get_string(),
		config_get_unit_id(),
		power_get_mains_string(),
		config_get_temp_max_string(),
		config_get_temp_min_string(),
		trigger_is_active(TRIGGER_MAINS),
		trigger_is_active(TRIGGER_DOOR),
		trigger_is_active(TRIGGER_BATTERY),
		trigger_is_active(TRIGGER_TEMPERATURE)
	);

	return USART0_SendCommand(command, "OK") == SYS_PASS;
}