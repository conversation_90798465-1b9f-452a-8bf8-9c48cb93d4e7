/*
 * Mqtt_send.c
 *
 * Created: 2020/01/04 11:42:58 AM
 *  Author: Bron<PERSON>
 */ 
 #include <string.h>
 #include <stdio.h>
 #include <stdlib.h>
 #include <avr/pgmspace.h>
 
 #include "types.h"
 #include "system.h"
 #include "lcd.h"
 #include "delay.h"
 #include "usart.h"
 #include "io_pins.h"
 #include "timer.h"
 #include "e2prom.h"
 #include "modem.h" 
 #include "mqtt.h"	
 #include "console.h"
 #include "config.h"
 #include "temperature.h"
 #include "power.h"
 #include "door.h"
 #include "state.h"
 #include "trigger.h"

 #define APN				"gigsky-02"
 #define MQTT_SERVER		"api.txdevsystems.co.za"
 #define MQTT_PORT			"1883"

bool mqtt_connect(void)
{	
	char cmd[128];
	
	if (!usart0_send_command("AT+SAPBR=3,1,\"CONTYPE\",\"GPRS\"", "OK"))
		return false;

	snprintf(cmd, sizeof(cmd), "AT+SAPBR=3,1,\"APN\",\"%s\"", APN);
	if (!usart0_send_command(cmd, "OK"))
		return false;

	if (!usart0_send_command("AT+SAPBR=1,1", "OK"))
		return false;

	if (!usart0_send_command("AT+SAPBR=2,1", "OK"))
		return false;

	snprintf(cmd, sizeof(cmd), "AT+SMCONF=\"URL\",\"%s\"", MQTT_SERVER);
	if (!usart0_send_command(cmd, "OK"))
		return false;

	if (!usart0_send_command("AT+SMCONF=\"CLEANSS\",1", "OK"))
		return false;

	if (!usart0_send_command("AT+SMCONN", "OK"))
		return false;

	return true;
}

bool mqtt_subscribe(char *topic)
{
	char cmd[64];

    snprintf(cmd, sizeof(cmd), "AT+SMSUB=\"%s\",1", topic);
	return usart0_send_command(cmd, "OK");
 }

bool mqtt_connect_and_subscribe(const char *imei)
{
	char topic[IMEI_LNGTH + 2];

	if (!mqtt_connect())
		return false;

	console_writeline("\r\n### MQTT connected\r\n");

	snprintf(topic, sizeof(topic), "C%s", config_get_imei());
	console_writeline("\r\n### MQTT Subscribe %s", topic);

	if (!mqtt_subscribe(topic))
	{
		console_writeline("### MQTT subscribe failed");
		return false;
	}

	return true;
}

/*
* TOPIC: txdev/cr/c1
* DATA:
*/
bool mqtt_publish_config(void)
{
	char command[256] = "";

	snprintf(command, sizeof(command),"%s%s", 
		"AT+SMPUB=\"txdev/cr/c1\",1,0,",
		config_get_string()
	);

	return usart0_send_command(command, "OK") == true;
}

/*
* TOPIC: txdev/cr/d1
* DATA:
*   - imei: string
*   - temp_set_max: string
*   - current_temperature: string
*   - temp_set_min: string
*   - door_status: 0/1
*   - psu_status: 0/1
*   - supply_volt: string
*   - battery_low_trigger: 0/1
*   - battery_volt: string
*   - location: string
*   - signal_strength: string
*/
bool mqtt_publish_data(void)
{
	char command[256] = "";

	snprintf(command, sizeof(command),"%s%s:%s:%s:%s:%d:%d:%s:%d:%s:%s:%d:", 
		"AT+SMPUB=\"txdev/cr/d1\",1,0,",
		config_get_imei(),
		config_get_temp_max_string(),
		temperature_get_string(),
		config_get_temp_min_string(),
		door_is_open() ? 1 : 0,
		trigger_is_active(TRIGGER_MAINS) ? 0 : 1,
		power_get_mains_string(),
		trigger_is_active(TRIGGER_BATTERY) ? 0 : 1,
		power_get_battery_string(),
		"",
		state_get_current()->signal_strength
	);

	return usart0_send_command(command, "OK") == true;
}

/*
* TOPIC: txdev/cr/t1
* DATA:
*   - imei: string
*   - current_temperature: string
*   - unit_id: string
*   - supply_volt: string
*   - temp_set_max: string
*   - temp_set_min: string
*   - psu_trigger: 0/1
*   - door_trigger: 0/1
*   - battery_low_trigger: 0/1
*   - temperature_trigger: 0/1
*/
bool mqtt_publish_trigger(void)
{
	char command[256] = "";

	snprintf(command, sizeof(command),"%s%s:%s:%s:%s:%s:%s:%d:%d:%d:%d:", 
		"AT+SMPUB=\"txdev/cr/t1\",1,0,",
		config_get_imei(),
		temperature_get_string(),
		config_get_unit_id(),
		power_get_mains_string(),
		config_get_temp_max_string(),
		config_get_temp_min_string(),
		trigger_is_active(TRIGGER_MAINS),
		trigger_is_active(TRIGGER_DOOR),
		trigger_is_active(TRIGGER_BATTERY),
		trigger_is_active(TRIGGER_TEMPERATURE)
	);

	return usart0_send_command(command, "OK") == true;
}