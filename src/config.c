#include <stdlib.h>

#include "types.h"
#include "e2prom.h"
#include "mqtt.h"
#include "console.h"

#include "config.h"

// Default startup config
static Config s_config = {
			.imei = "000000000000000",
			.iccid = "0000000000000000",
			.unitId = "NOTSET",
			.location = "",
			.revision = VERS,
			.doorAlarmMinute = 5,
			.dataResendMinute = 20,
			.triggerResendMinute = 5,
			.doorPolarity = 0,
			.tempMin = -40, // -4.0 degrees
			.tempMax = 40,  // **** degrees
		};

const Config *config_get(void)
{
	return &s_config;
}

const char *config_get_string(void)
{
	static char config_string[128];
	snprintf(config_string, sizeof(config_string), "%s:%s:%s:%s:%d:%d:%d:%s:%s:%d:",
		s_config.imei,
		s_config.iccid,
		s_config.unitId,
		s_config.revision,
		s_config.doorPolarity,
		s_config.dataResendMinute,
		s_config.doorAlarmMinute,
		config_get_temp_max_string(),
		config_get_temp_min_string(),
		s_config.triggerResendMinute);
	return config_string;
}

const char *config_get_imei(void)
{
	return s_config.imei;
}

void config_set_imei(const char *imei)
{
	strncpy(s_config.imei, imei, IMEI_LNGTH);
	s_config.imei[IMEI_LNGTH] = '\0';
}

const char *config_get_iccid(void)
{
	return s_config.iccid;

}

void config_set_iccid(const char *iccid)
{
	strncpy(s_config.iccid, iccid, IMEI_LNGTH);
	s_config.iccid[ICCID_LNGTH] = '\0';
}

const char *config_get_unit_id(void)
{
	return s_config.unitId;
}

const char *config_get_revision(void)
{
	return s_config.revision;
}

const char *config_get_temp_min_string(void)
{
	static char temp_min_string[8];
	snprintf(temp_min_string, sizeof(temp_min_string), "%d.%d", s_config.tempMin / 10, abs(s_config.tempMin) % 10);
	return temp_min_string;
}

const char *config_get_temp_max_string(void)
{
	static char temp_max_string[8];
	snprintf(temp_max_string, sizeof(temp_max_string), "%d.%d", s_config.tempMax / 10, abs(s_config.tempMax) % 10);
	return temp_max_string;
}

uint8_t config_get_trigger_minutes(void)
{
	return s_config.triggerResendMinute;
}

uint8_t config_get_data_minutes(void)
{
	return s_config.dataResendMinute;
}	

bool config_read_from_eeprom(void)
{
	console_writeline("Loading config from EEprom:");	

	EEPROM_RD_string(EEPROM_IMEI_ADDR, s_config.imei, sizeof(s_config.imei));
	console_writeline(" IMEI: %s", s_config.imei);

	EEPROM_RD_string(EEPROM_ICCID_ADDR, s_config.iccid, sizeof(s_config.iccid));
	console_writeline(" ICCID: %s", s_config.iccid);

	EEPROM_RD_string(EEPROM_UNITID_ADDR, s_config.unitId, sizeof(s_config.unitId));
	console_writeline(" Unit ID: %s", s_config.unitId);

	s_config.location[0] = '\0';

	EEPROM_RD_string(EEPROM_REVISION_ADDR, s_config.revision, sizeof(s_config.revision));
	console_writeline(" Revision: %s", s_config.revision);
	
	s_config.doorAlarmMinute = EEPROM_RD_byte(EEPROM_DOOR_ALARM_MINUTE_ADDR);
	if (s_config.doorAlarmMinute == 0xFF) // Check for uninitialized EEPROM
		s_config.doorAlarmMinute = 5;
    console_writeline(" Door Alarm Minute: %d", s_config.doorAlarmMinute);	

	s_config.dataResendMinute = EEPROM_RD_byte(EEPROM_DATA_RESEND_MINUTE_ADDR);
	if (s_config.dataResendMinute == 0xFF)
		s_config.dataResendMinute = 20;
    console_writeline(" Data Resend Minute: %d", s_config.dataResendMinute);

	s_config.triggerResendMinute = EEPROM_RD_byte(EEPROM_TRIGGER_RESEND_MINUTE_ADDR);
	if (s_config.triggerResendMinute == 0xFF)
		s_config.triggerResendMinute = 5;
    console_writeline(" Trigger Resend Minute: %d", s_config.triggerResendMinute);

	s_config.doorPolarity = EEPROM_RD_byte(EEPROM_DOOR_POLARITY_ADDR);
	if (s_config.doorPolarity > 1)
		s_config.doorPolarity = 0;
	console_writeline(" Door Polarity: %d", s_config.doorPolarity);

	// Read as 16-bit values (2 bytes each)
	s_config.tempMin = (EEPROM_RD_byte(EEPROM_TEMP_MIN_ADDR) | (EEPROM_RD_byte(EEPROM_TEMP_MIN_ADDR + 1) << 8));
	if (s_config.tempMin == -1 || s_config.tempMin < -500 || s_config.tempMin > 500) // Sanity check
		s_config.tempMin = -40; // Default -4.0 degrees
	console_writeline(" Temp Min: %d (0x%04X) as bytes 0x%02X, 0x%02X",
		s_config.tempMin, s_config.tempMin, 
		s_config.tempMin & 0xFF, (s_config.tempMin >> 8) & 0xFF);

	s_config.tempMax = (EEPROM_RD_byte(EEPROM_TEMP_MAX_ADDR) | (EEPROM_RD_byte(EEPROM_TEMP_MAX_ADDR + 1) << 8));
	if (s_config.tempMax == -1 || s_config.tempMax < -500 || s_config.tempMax > 500) // Sanity check
		s_config.tempMax = 40; // Default **** degrees
	console_writeline(" Temp Max: %d (0x%04X) as bytes 0x%02X, 0x%02X",
		s_config.tempMax, s_config.tempMax,
		s_config.tempMax & 0xFF, (s_config.tempMax >> 8) & 0xFF);

	console_writeline(" EEPROM CONFIG := [%s]", config_get_string());
	return true;
}

bool config_write_to_eeprom(void)
{
	console_writeline("Updating config in EEPROM");

	console_writeline("Writing IMEI: %s (len=%d)", s_config.imei, strlen(s_config.imei));
	EEPROM_WR_string(EEPROM_IMEI_ADDR, s_config.imei, strlen(s_config.imei));
	
	console_writeline("Writing ICCID: %s (len=%d)", s_config.iccid, strlen(s_config.iccid));
	EEPROM_WR_string(EEPROM_ICCID_ADDR, s_config.iccid, strlen(s_config.iccid));
	
	console_writeline("Writing Unit ID: %s", s_config.unitId);
	EEPROM_WR_string(EEPROM_UNITID_ADDR, s_config.unitId, sizeof(s_config.unitId));

	strcpy(s_config.revision, VERS);
	console_writeline("Writing Revision: %s", s_config.revision);
	EEPROM_WR_string(EEPROM_REVISION_ADDR, s_config.revision, strlen(s_config.revision));

	console_writeline("Writing Door Alarm Minute: %d", s_config.doorAlarmMinute);
	EEPROM_WR_byte(EEPROM_DOOR_ALARM_MINUTE_ADDR, s_config.doorAlarmMinute);
	
	console_writeline("Writing Data Resend Minute: %d", s_config.dataResendMinute);
	EEPROM_WR_byte(EEPROM_DATA_RESEND_MINUTE_ADDR, s_config.dataResendMinute);
	
	console_writeline("Writing Trigger Resend Minute: %d", s_config.triggerResendMinute);
	EEPROM_WR_byte(EEPROM_TRIGGER_RESEND_MINUTE_ADDR, s_config.triggerResendMinute);
	
	console_writeline("Writing Door Polarity: %d", s_config.doorPolarity);
	EEPROM_WR_byte(EEPROM_DOOR_POLARITY_ADDR, s_config.doorPolarity);

	// Write 16-bit temperature values (2 bytes each)
	console_writeline("Writing Temp Min: %d (0x%04X) as bytes 0x%02X, 0x%02X", 
		s_config.tempMin, s_config.tempMin, 
		s_config.tempMin & 0xFF, (s_config.tempMin >> 8) & 0xFF);
	EEPROM_WR_byte(EEPROM_TEMP_MIN_ADDR, s_config.tempMin & 0xFF);
	EEPROM_WR_byte(EEPROM_TEMP_MIN_ADDR + 1, (s_config.tempMin >> 8) & 0xFF);
	
	console_writeline("Writing Temp Max: %d (0x%04X) as bytes 0x%02X, 0x%02X", 
		s_config.tempMax, s_config.tempMax,
		s_config.tempMax & 0xFF, (s_config.tempMax >> 8) & 0xFF);
	EEPROM_WR_byte(EEPROM_TEMP_MAX_ADDR, s_config.tempMax & 0xFF);
	EEPROM_WR_byte(EEPROM_TEMP_MAX_ADDR + 1, (s_config.tempMax >> 8) & 0xFF);

	console_writeline("Config written to EEPROM successfully");
	return true;
}

void config_parse_data(char *data)
{
	char *token = strtok(data, ":");
	int index = 0;
	while (token != NULL)
	{
		switch (index)
		{
		case 0: // Skip first :
			break;
		case 1:
			s_config.tempMax = (int16_t)atof(token) * 10;
			break;
		case 2:
			s_config.tempMin = (int16_t)atof(token) * 10;
			break;
		case 3:
			s_config.doorPolarity = atoi(token);
			break;
		case 4:
			s_config.dataResendMinute = atoi(token);
			break;
		case 5:
			s_config.doorAlarmMinute = atoi(token);
			break;
		case 6:
			// reqLocation = atoi(token);
			break;
		case 7:
			strncpy(s_config.unitId, token, sizeof(s_config.unitId) - 1);
			s_config.unitId[sizeof(s_config.unitId) - 1] = '\0';
			break;
		case 8:
			s_config.triggerResendMinute = atoi(token);
			break;
		}
		index++;
		token = strtok(NULL, ":");
	}
}

bool config_from_message(char *data)
{
	int length = 0;

	char *tokenString = strdup(data);
	if (!tokenString)
	{
		console_writeline("Out of memory");
		return false;
	}

	int index = 0;
	char *token = strtok(tokenString, ",");
	while (token != NULL)
	{
		switch (index)
		{
		case 0:
			// Ignore QoS
			break;
		case 1:
			// Check IMEI (skip the leading "C and trailing ")
			token[IMEI_LNGTH + 2] = '\0';
			if (strcmp(&token[2], s_config.imei) != 0)
			{
				console_writeline("Not my config message");
				free(tokenString);
				return false;
			}
			break;
		case 2:
			length = atoi(token);
			break;
		case 3:
			// Sanity check, config string should end in ;
			if (token[length] != ';')
			{
				console_writeline("Not a valid config message");
				free(tokenString);
				return false;
			}
			else
			{
				config_parse_data(token);
				config_write_to_eeprom();
			}
			break;
		}
		index++;
		token = strtok(NULL, ",");
	}
	free(tokenString);
	return true;
}
