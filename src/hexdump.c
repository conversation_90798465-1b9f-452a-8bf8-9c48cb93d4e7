#include <stdio.h>
#include <ctype.h>

#include "console.h"

void hex_dump(char *pad, char *data, int length)
{
    for (int i = 0; i < length; i += 16) 
    {
        console_write(pad);

        // Print the hex values
        for (int j = i; j < i + 16; j++)
        {
            if (j < length)
            {
                console_write("%02X ", data[j]);
            }
            else
            {
                console_write("   ");
            }
        }

        console_write("|");

        // Print the ASCII characters
        for (int j = i; j < i + 16; j++)
        {
            if (j < length)
            {
                unsigned char c = (data[j] < 32 || data[j] > 126) ? '.' : data[j];
                console_write("%c", c);
            }
            else
            {
                console_write(" ");
            }
        }

        console_writeline("|");
    }
}
