/*************************************************************************
* Title: main.c
-----------------------------------------------------------------------------
Company         : TXD Systems
Module          : Project generic defines
Purpose         :
Notes           :
History         :
Date			: 24/03/2018
dd/mm/yyyy  Revision   Description
----------  ---------   -----------
14/06/2024:
* From VERS "4.11" the Telegram will be removed. It will be set on the Host server.
* When the device has been configured a Config response must be sent with a
 "txdev/cr/c1" in the header.
 * The Unit ID will also be received from the Host server. This will be include
	this version.
* WEBSRV_DIFF_BIT - used when there is an config update from the server,
must be used in main.c to cause parameters to be updated.
------------------------------------------------------------------------------*/
#include <avr/io.h>
#include <stdlib.h>

#include "types.h"
#include "system.h"
#include "lcd.h"
#include "lcd_def.h"
#include "delay.h"
#include "delay.h"
#include "usart.h"
#include "console.h"
#include "modem.h"
#include "timer.h"
#include "io_pins.h"
#include "e2prom.h"
#include "mqtt.h"
#include "OW_DS1820.h"

#include "config.h"
#include "trigger.h"
#include "door.h"
#include "temperature.h"
#include "power.h"
#include "state.h"

static uint8_t s_bit = 0;
#define HEART_BEAT PinWR(LED_D1, &PORTD, s_bit) ; s_bit = ~s_bit ;

#define INTERVAL_SYSCHECK 1

#define STATE_OK 0x00
#define STATE_MQTT_DISCONNECTED 0x01
#define STATE_MODEM_DISCONNECTED 0x02

uint8_t state = STATE_MODEM_DISCONNECTED;

/* Global variable definitions:
-------------------------------*/
volatile uint16_t tick_SEC = 0;

bool publish_data(uint16_t tick)
{
	static int last_data_tick = 0;

	if (tick < last_data_tick) {
		last_data_tick = 0;
	}	

	// force data publish if door state changed
	if (state_door_changed())
		last_data_tick = 0;

	if ((last_data_tick == 0) || ((tick - last_data_tick) >= config_get_data_minutes() * 60)) {
		console_writeline("Publishing data to MQTT (%u ticks)...", tick - last_data_tick);
		state_update_signal_strength(modem_RSSI());
		if (!mqtt_publish_data()) {
			console_writeline("### MQTT data publish failed");
			return false;
		}
		last_data_tick = tick;
	} else {
		console_writeline("Skipping MQTT data publish, interval not reached (%u/%u ticks since last).", tick - last_data_tick, config_get_data_minutes() * 60);
	}

	return true;
}

void check_triggers(uint16_t tick)
{
	if (state_power_mains_is_low()) {
		console_writeline("! Mains low");
		trigger_raise(TRIGGER_MAINS, tick);
	} else {
		trigger_lower(TRIGGER_MAINS);
	}
	
	if (state_power_battery_is_low()) {
		console_writeline("! Battery low");
		trigger_raise(TRIGGER_BATTERY, tick);
	} else {
		trigger_lower(TRIGGER_BATTERY);
	}
	
	if (state_temperatue_is_low() || state_temperature_is_high()) {
		if (state_temperatue_is_low()) {
			console_writeline("! Temperature low");
		}
		if (state_temperature_is_high()) {
			console_writeline("! Temperature high");
		}
		trigger_raise(TRIGGER_TEMPERATURE, tick);
	} else {
		trigger_lower(TRIGGER_TEMPERATURE);
	}
	
	if (state_door_is_open()) {
		console_writeline("! Door open");
		trigger_raise(TRIGGER_DOOR, tick);
	} else {
		trigger_lower(TRIGGER_DOOR);
	}
}

bool publish_triggers(uint16_t tick)
{
	static int last_trigger_tick = 0;

	if (tick < last_trigger_tick) {
		last_trigger_tick = 0;
	}	

	bool send_trigger = false;
	for (int i = 0; i < TRIGGER_COUNT; i++) {
		console_writeline("Trigger %s: %s", TriggerNames[i], trigger_is_active(i) ? "Active" : "Inactive");
		if (trigger_should_alarm(i)) {
			console_writeline("  -> Should alarm");
			send_trigger = true;
		}
	}

	if (send_trigger) {
		if ((last_trigger_tick == 0) || ((tick - last_trigger_tick) >= config_get_trigger_minutes() * 60)) {
			console_writeline("Publishing trigger to MQTT (%u ticks)...", tick - last_trigger_tick);
			if (!mqtt_publish_trigger()) {
				console_writeline("### MQTT trigger publish failed");
				return false;
			}
			last_trigger_tick = tick;
		} else {
			console_writeline("Skipping MQTT trigger publish, interval not reached (%u/%u ticks since last).", tick - last_trigger_tick, config_get_trigger_minutes() * 60);
		}
	}

	return true;
}

bool system_monitor_and_publish(uint16_t tick, bool should_publish)
{
	state_update();
	state_print();
	check_triggers(tick);

	return;
	
	if (should_publish)
	{
		if (!publish_data(tick)) {
			console_writeline("### Data publish failed.");
			return false;
		}

		if (!publish_triggers(tick)) {
			console_writeline("### Trigger publish failed.");
			return false;
		}
	}

	return true;
}

void start_page()
{
	lcd_clear() ;
	lcd_writeline(LCD_LINE_1, "  TXDEVSYSTEMS");
	lcd_writeline(LCD_LINE_2, "COLD ROOM v%s" VERS);
}

void update_display(uint8_t ticks)
{
	// Update display every 5 ticks, or every 60 ticks if no triggers are active
	if (ticks % 5 != 0)
		return;

	if (!trigger_is_active(TRIGGER_ANY) || ticks % 60 == 0) {
		char state_string[3];

		if (state == STATE_OK) {
			strcpy(state_string, "OK");
		} else {
			snprintf(state_string, sizeof(state_string), "%02X", state);
		}

		lcd_writeline(LCD_LINE_1, "STATUS: %s    %02d", state_string, state_get_signal_strength());
		lcd_writeline(LCD_LINE_2, "%s %s %s", 
				config_get_temp_min_string(),
				temperature_get_string(),
				config_get_temp_max_string());
	} else {
		lcd_writeline(LCD_LINE_1, "%s %s %s %s",
			 TriggerNames[0], trigger_is_active(0) ? "Lo" : "OK",
			 TriggerNames[1], trigger_is_active(1) ? "Lo" : "OK");
		lcd_writeline(LCD_LINE_2, "%s %s %s %s",
			 TriggerNames[2], trigger_is_active(2) ? "Op" : "OK",
			 TriggerNames[3], trigger_is_active(3) ? state_temperatue_is_low() ? "Lo" : "Hi" : "OK");
	}
}

void load_config()
{
	if (!config_read_from_eeprom()) {
		console_writeline("### Config read from EEPROM failed, using defaults");
	}
	const Config* config = config_get();
	state_init(config->tempMin, config->tempMax);
	console_writeline("Temperature min: %s, max: %s, battery cutoff: %d, door alarm: %d",
		 config_get_temp_min_string(),
	     config_get_temp_max_string(),
		 5,
		 config->doorAlarmMinute);
}

void read_modem_data(void)
{
	char command[16 + 1];
	char data[128 + 1];
	char message[256];
	uint8_t size;

	// Read MQTT messages and status updates from modem
	if ((size = usart0_get_unsolicited_data(message, sizeof(message))) <= 0)
		return;

	if (sscanf(message, "+%16[^:]: %128s", command, data) != 2)
		return;

	console_write("\r\n+++ Command: [");
	console_write(command);
	console_write("] data: [");
	console_write(data);
	console_writeline("]");

	if (strcmp(command, "SMPUBLISH") == 0)
	{
		if (config_from_message(data))
			mqtt_publish_config();
	}
	else if (strcmp(command, "SMSUB") == 0)
	{
		console_writeline("Subscribed to MQTT");
	}
	else if (strcmp(command, "SMPUB") == 0)
	{
		// Ignore
	}
	else
	{
		console_write("Command [");
		console_write(command);
		console_writeline("] not implemented");
	}
}

int main(void)
{
	uint16_t tick_SEC_old = 0;
	uint16_t ticks = 0;
	bool mqtt_connected = true;
	bool modem_initialized = true;

	lcd_init();
	start_page();

	console_init();
	console_writeline("\r\n### Start ###\r\n");

	door_init();
	Timer3_CTC_init(); // Start timer 3 in timer.c
	PinWR(BATCUTOFF, &PORTB, 0); /* Write 0 to switch P=Chan ON */
	RomReaderProgram(); // Read the ID of the Dallas 1820 device
	ds1820_initialise();

	load_config();
	state_update();

	delay_ms(1000);
	console_writeline("### System initialized ###");

	while (true)
	{
		console_writeline("\r\n\t\t### Main loop (%u) ###", ticks);

		if (!modem_initialized)
			state = STATE_MODEM_DISCONNECTED;
		else if (!mqtt_connected)
			state = STATE_MQTT_DISCONNECTED;
		else
			state = STATE_OK;

		update_display(ticks);

		if (tick_SEC != tick_SEC_old)
		{
			ticks++;

			HEART_BEAT

			tick_SEC_old = tick_SEC;

			if (!(ticks % INTERVAL_SYSCHECK))
			{
				if (mqtt_connected) {
					if (!system_monitor_and_publish(tick_SEC, mqtt_connected))
					{
						console_writeline("### System monitor and publish failed, reconnecting...");
						mqtt_connected = false;
						modem_initialized = false;
						continue;
					}	
				}
			}
		}

		if (!modem_initialized)
		{
			mqtt_connected = false;
			console_writeline("\r\n### Modem Initialization started...");

			if (!modem_is_power_on())
			{

				console_writeline("### Modem power up...");
				if (!modem_power_on())
				{
					console_writeline("### Modem power up failed, retrying");
					continue;
				}

				console_writeline("### Modem power up OK");
				if (!modem_init())
					continue;

				config_set_imei(modem_IMEI());
				config_set_iccid(modem_ICCID());

				console_writeline("\r\n### Modem Initialization OK");
				modem_initialized = true;
			}
			else
			{
				console_writeline("### Turning modem off");
				modem_power_off();
			}

			continue;
		}

		if (!mqtt_connected)
		{
			console_writeline("### MQTT initialization started...\r\n");

			mqtt_connected = mqtt_connect_and_subscribe(config_get_imei());
			if (!mqtt_connected)
			{
				console_writeline("### MQTT connect failed");
				modem_initialized = false;
				continue;
			}

			if (!mqtt_publish_config())
			{
				console_writeline("### MQTT Config publish failed");
				mqtt_connected = false;
				modem_initialized = false;
			}

			continue;
		}

		read_modem_data();

		delay_ms(1000);
    }
}