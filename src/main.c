/*************************************************************************
* Title: main.c
-----------------------------------------------------------------------------
Company         : TXD Systems
Module          : Project generic defines
Purpose         :
Notes           :
History         :
Date			: 24/03/2018
dd/mm/yyyy  Revision   Description
----------  ---------   -----------
14/06/2024:
* From VERS "4.11" the Telegram will be removed. It will be set on the Host server.
* When the device has been configured a Config response must be sent with a
 "txdev/cr/c1" in the header.
 * The Unit ID will also be received from the Host server. This will be include
	this version.
* WEBSRV_DIFF_BIT - used when there is an config update from the server,
must be used in main.c to cause parameters to be updated.
------------------------------------------------------------------------------*/
#include <avr/io.h>
#include <stdlib.h>

#include "types.h"
#include "system.h"
#include "lcd.h"
#include "lcd_def.h"
#include "delay.h"
#include "delay_c.h"
#include "usart.h"
#include "console.h"
#include "modem.h"
#include "timer.h"
#include "interrupts.h"
#include "io_pins.h"
#include "e2prom.h"
#include "mqtt.h"
#include "main.h"
#include "OW_DS1820.h"
#include "DHT22.h"
#include "hexdump.h"

#include "config.h"
#include "trigger.h"
#include "door.h"
#include "temperature.h"
#include "power.h"
#include "state.h"

#define MQTTDATA 21
#define MQTTCONFIG 22
#define MQTTTRIGG 23
#define CONTINUE_STAT 0
#define EXIT_STAT 1
#define UPDATE_NORM 0x00
#define UPDATE_YES 0x01
// #define TRIG_UPDATE_YES		0x02
#define CONFIG_UPDATE_YES 0x04
#define MQTT_CONF_UPDATE_YES 0x10
#define STATE_NOT_SET 11
#define INTERVAL_SYSCHECK 5			/* time to check system status */
#define INTERVAL_SYSERRDSPLY 10		/* time to display Telegram and ID warning if not set */
#define INTERVAL_SERV_CONN_RETRY 45 /* time to retry to conn to Server if prev failed*/
#define INTRVL_MSG_DSPLY 5			/* time to keep message on display */
#define INTRVL_NOCNF_DSPLY 5		/* time to display "Config Not set" */
// #define INTERVAL_LCD_OFF 120		/*120	Seconds = 2 min */
#define INTERVAL_LCD_OFF 5 /*5 Seconds */
// #define INTERVAL_MQTT_TX  1200	/*1200s = 20 min */
#define INTERVAL_MQTT_TX 60
#define ALARM_TRIG_CNT 6
#define TSK0_INTVAL 20
#define TIMEOUT 0	 /* Timeout indicated by "0"*/
#define NO_TIMEOUT 1 /* Timeout indicated by "1"*/

#define MQTT_DATA_INTERVAL 1200 /* 1200s = 20 min */
#define MQTT_TRIGGER_INTERVAL 300 /* 300 = 5 min */

/* Compiler directives - Hash defines
-------------------------------------*/
// #define DHT22_SENSOR_
// #define _TEMPERATURE_CTRL_MODE_
// #define _GPS_L80_
#ifndef _GPS_L80_
#define _DEBUG_ /* !!!Debug port is used by GPS !!!*/
#endif

#define _WEB_SERVER_CONFIG_
// #define JUSTWORX

/* Global variable definitions:
-------------------------------*/
/* Global Variables used in ISR must be type volatile */
volatile UI_8 tick_interval = 0; /* for interval countdown under 4 min */
volatile UI_16 tick_SEC = 0;
volatile UI_16 tick_SEC_old = 0;
UI_8 last_lcd_cnt = 0;

uint8_t Set_Temp_High;
int8_t Set_Temp_Low;
// uint8_t Count_To_Task_1 = 20;
UI_8 Count_To_Task_1 = TSK0_INTVAL; /*Time allowed for displaying menu items before returing to default screen*/
uint8_t TaskActive = 1;				// If 1 then write to LCD
int8_t Task = 1;					// Define Display Task
uint8_t LCDUpdatNow = 0;
uint8_t UpdateLCD = 1;
uint8_t Set_Door_Warning = 0;

/* Global to reduce stack requirements */
UI_8 State_nr = 0;
UI_16 retry = 0;
UI_32 dly = 0;

int DoorAlarmCnt = 0;
int trig_cnt = 0;
UI_8 Prev_Status; // can be changed by functions, stack var is not reliable

extern volatile UI_8 UART_RxD;
extern UI_8 eeprom_updated;
// volatile UI_8 eepromstatusbits = 0 ;
UI_8 eepromstatusbits = 0;

bool modem_initialized = false;

/* External functions:
----------------------*/
extern void Menu_SysSet(mqtt_data_struct *mqtt_dataptr);

/* Functions prototype:
----------------------*/
void Error_Code_Display(UI_8 code);
void Check_JTAG(void);
void Debug_RxIntr(void);
void Debug_Gothere(UI_8 nr);
void Timer1_Test(void);
void Show_StackPtr(void);
void SysStatCheck(mqtt_data_struct *mqtt_dataptr);
void delay_ms_1(uint16_t ms);
void HandleModemData(char *message, UI_8 msgSize);
void system_monitor_and_publish(uint16_t tick);

char topic[IMEI_LNGTH + 2];
char message[256];
UI_8 mqtt_connected = 0;

void system_monitor_and_publish(uint16_t tick)
{
	static int last_data_tick = 0;
	static int last_trigger_tick = 0;

	if (tick < last_data_tick) {
		// Reset last_trigger_tick if it has wrapped around
		last_data_tick = 0;
	}	

	if (tick < last_trigger_tick) {
		// Reset last_trigger_tick if it has wrapped around
		last_trigger_tick = 0;
	}	

	int changed = state_changed();
	const State *curr_state = state_get_current();

	console_writeline("");
	console_writeline("Current state (tick %u)%s:", tick, changed ? " changed" : "");
	console_writeline("-----------------------------------------");
	console_writeline("Mains: %sV", power_get_mains_string());
	console_writeline("Battery: %sV", power_get_battery_string());
	console_writeline("Temperature: %s", temperature_get_string());
	console_writeline("Door: %s", curr_state->is_door_open ? "Open" : "Closed");

	// if (changed)
	{
		if (state_power_mains_is_low()) {
			console_writeline("! Mains low");
			trigger_raise(TRIGGER_MAINS, tick);
		} else {
			trigger_lower(TRIGGER_MAINS);
		}
		
		if (state_power_battery_is_low()) {
			console_writeline("! Battery low");
			trigger_raise(TRIGGER_BATTERY, tick);
		} else {
			trigger_lower(TRIGGER_BATTERY);
		}
		
		if (state_temperatue_is_low() || state_temperature_is_high()) {
			if (state_temperatue_is_low()) {
				console_writeline("! Temperature low");
			}
			if (state_temperature_is_high()) {
				console_writeline("! Temperature high");
			}
			trigger_raise(TRIGGER_TEMPERATURE, tick);
		} else {
			trigger_lower(TRIGGER_TEMPERATURE);
		}
		
		if (state_door_is_open()) {
			console_writeline("! Door open");
			trigger_raise(TRIGGER_DOOR, tick);
		} else {
			trigger_lower(TRIGGER_DOOR);
		}
	}

	if ((last_data_tick == 0) || ((tick - last_data_tick) >= config_get_data_minutes() * 60)) {
		console_writeline("Publishing data to MQTT (%u ticks)...", tick - last_data_tick);
		if (!DataPublish()) {
			console_writeline("### MQTT data publish failed");
			modem_initialized = false;
			return;
		}
		last_data_tick = tick;
	} else {
		console_writeline("Skipping MQTT data publish, interval not reached (%u/%u ticks since last).", tick - last_data_tick, config_get_data_minutes() * 60);
	}

	bool send_trigger = false;
	for (int i = 0; i < TRIGGER_COUNT; i++) {
		console_writeline("Trigger %s: %s", TriggerNames[i], trigger_is_active(i) ? "Active" : "Inactive");
		if (trigger_should_alarm(i)) {
			console_writeline("  -> Should alarm");
			send_trigger = true;
		}
	}

	if (send_trigger) {
		if ((last_trigger_tick == 0) || ((tick - last_trigger_tick) >= config_get_trigger_minutes() * 60)) {
			console_writeline("Publishing trigger to MQTT (%u ticks)...", tick - last_trigger_tick);
			if (!TriggerPublish()) {
				console_writeline("### MQTT trigger publish failed");
				modem_initialized = false;
				return;
			}
			last_trigger_tick = tick;
		} else {
			console_writeline("Skipping MQTT trigger publish, interval not reached (%u/%u ticks since last).", tick - last_trigger_tick, config_get_trigger_minutes() * 60);
		}
	}
}

/*--------------------------------------------------------------------
  Function   : StartPage()
  Purpose    : Display on power-up.
  Parameters :
  Returns    : 
  Notes      : In Tasks where the menu item is only displayed and the turn
					of the RE is not used the Task++ must NOT be used!
----------------------------------------------------------------------*/
void StartPage()
{
	lcd_Clear() ;
	#ifdef DHT22_SENSOR_
	lcd_display_string("HUMIDITY V"VERS, 0, LCD_LINE_1);
	lcd_display_string("TXDEVSYSTEMS.COM", 0, LCD_LINE_2);
	#else
	lcd_display_string("    TMS V"VERS, 0, LCD_ROW1);
	//lcd_display_string("COLD ROOM V"VERS, 0, LCD_LINE_1);
	lcd_display_string("TXDEVSYSTEMS.COM", 0, LCD_LINE_2);
	#endif
	if (Task != 10)
	{
		delay_ms_1(2000)	;
	}
}

int main(void)
{
	uint8_t i = 0;
	uint16_t bit = 0;
	uint16_t ticks = 0; /* Use for system check & update interval*/

	door_init();

	lcd_on();
	lcd_Init();
	lcd_Custom_Char_Dsply(); /* Create special custom character in CGRAM */

	console_init();
	console_writeline("\r\n### Start ###\r\n");

	Timer3_CTC_init(); // Start timer 3 in timer.c

	PinWR(BATCUTOFF, &PORTB, 0); /* Write 0 to switch P=Chan ON */

	StartPage();

	RomReaderProgram(); // Read the ID of the Dallas 1820 device
	ds1820_Initialise();

	//TODO - Move to function to initialize state
	if (!config_read_from_eeprom()) {
		console_writeline("### Config read from EEPROM failed, using defaults");
	}
	const Config* config = config_get();
	state_init(config->tempMin, config->tempMax);
	console_writeline("Temperature min: %s, max: %s, battery cutoff: %d, door alarm: %d",
		 config_get_temp_min_string(),
	     config_get_temp_max_string(),
		 5,
		 config->doorAlarmMinute);

	/************************************************************************
	 Beginning of main loop:
	*************************************************************************/

	while (1)
	{
		delay_ms_1(1000);
		console_writeline("\r\n\t\t### Main loop (%u) ###", ticks);

		if (tick_SEC != tick_SEC_old)
		{
			ticks++;
			tick_interval++;
			if (tick_interval > 254)
				tick_interval = 0;

			/* Heart beat indicator:
			------------------------*/
			HEART_BEAT

			tick_SEC_old = tick_SEC;

#if 0
			/* This is the count down since last Rotary Encode action to reset to task 1
			----------------------------------------------------------------------------*/
			if ((Count_To_Task_1 == 0) && (Task != 1) &&
				!(mqtt_ptr->Stat_reg_2 & KEEP_DSPLY_NOCNF))
			{
				Task = 1; // return back to Task 1
				LCDUpdatNow = 1;
				TaskActive = 1;
				lcd_Clear();
			}

			/* ============================
				Switch LCD OFF after 2 min.
			------------------------------*/
			last_lcd_cnt++;
			if (last_lcd_cnt >= INTERVAL_LCD_OFF)
			{
				PinWR(LCD_SW, &PORTC, 1);
				last_lcd_cnt = 0;
			}
#endif

			if (!(ticks % INTERVAL_SYSCHECK))
			{
				console_writeline("\n\tGOT to INTERVAL_SYSCHECK");

				if (mqtt_connected == 1)
				{
					system_monitor_and_publish(tick_SEC);
				}
			}
		}


		// TODO
		// modem_initialized = 1;
		if (!modem_initialized)
		{
			mqtt_connected = 0;
			console_writeline("\r\n\r\n### Modem Initialization started...");

			if (Status_Pin_Check() == SYS_FAIL)
			{
				console_writeline("### Waiting for modem power up");

				if (Modem_Pwr_ON() == SYS_FAIL)
				{
					console_writeline("### Modem power up failed, retrying");
					continue;
				}
				console_writeline("### Modem power up OK");
				if (ModemInit() == SYS_FAIL)
					continue;

				// if (Modem_USSD_GetDataBalance() == SYS_FAIL)
				// continue;

				console_writeline("### Getting IMEI ...");

				const char *imei = Modem_IMEI();
				if (imei == NULL)
					continue;
				config_set_imei(imei);

				const char *iccid = Modem_ICCID();
				if (iccid == NULL)
					continue;
				config_set_iccid(iccid);
			}
			else
			{
				console_writeline("### Turning modem off");
				Modem_Pwr_OFF();
				continue;
			}

			console_writeline("\r\n\r\n### Modem Initialization OK");
			modem_initialized = true;
		}

		// TODO
		// mqtt_connected = 1;
		if (!mqtt_connected)
		{
			lcd_Clear();
			lcd_display_string("Connecting to", 1, LCD_LINE_1);
			lcd_display_string("Server...", 4, LCD_LINE_2);

			console_writeline("### MQTT initialization started...\r\n");

			if (MQTT_Init_Connect() == SYS_PASS)
			{
				console_writeline("\r\n### MQTT connected\r\n");

				snprintf(topic, sizeof(topic), "C%s", config_get_imei());
				console_writeline("\r\n### MQTT Subscribe %s", topic);

				if (MQTT_SubscribeTopic(topic) != SYS_PASS)
				{
					console_writeline("### MQTT subscribe failed");
					continue;
				}
			}
			else
			{
				console_writeline("### MQTT connect failed");
				modem_initialized = false;
				continue;
			}
			mqtt_connected = 1;

			lcd_Clear();
			lcd_display_string("MQTT connected", 0, LCD_LINE_1);

			if (!ConfigPublish())
			{
				console_writeline("### MQTT Config publish failed");
				mqtt_connected = 0;
				modem_initialized = false;
				continue;
			}

			state_changed();
		}

		// Read MQTT messages and status updates from modem
		if ((i = USART0_GetUnsolicitedData(message, 256)) > 0)
		{
			HandleModemData(message, i);
		}
	}
}

/*-----------------------------------------------------------
 Function   : delay_ms_1()
 Purpose    :
 Parameters :
 Returns    :
 Notes      :   Adjust this value based on your exact
					CPU frequency and loop overhead
------------------------------------------------------------*/
#define CYCLES_PER_MS (F_CPU / 1000 / 4) // Assuming 4 cycles per loop iteration

void delay_ms_1(uint16_t ms)
{
	while (ms--)
	{
		uint16_t count = CYCLES_PER_MS;
		while (count--)
		{
			asm volatile("nop");
		}
	}
}

void HandleModemData(char *message, UI_8 msgSize)
{
	char command[16 + 1];
	char data[128 + 1];

	if (sscanf(message, "+%16[^:]: %128s", command, data) != 2)
		return;

	console_write("\r\n+++ Command: [");
	console_write(command);
	console_write("] data: [");
	console_write(data);
	console_writeline("]");

	if (strcmp(command, "SMPUBLISH") == 0)
	{
		if (config_from_message(data))
			ConfigPublish();
	}
	else if (strcmp(command, "SMSUB") == 0)
	{
		console_writeline("Subscribed to MQTT");
	}
	else if (strcmp(command, "SMPUB") == 0)
	{
		// Ignore
	}
	else
	{
		console_write("Command [");
		console_write(command);
		console_writeline("] not implemented");
	}
}