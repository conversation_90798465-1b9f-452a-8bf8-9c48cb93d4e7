#include "trigger.h"

// Trigger thresholds (in ticks/seconds)
#define TRIGGER_DOOR_THRESHOLD 5        // Door open alarm after 5 seconds
#define TRIGGER_BATTERY_THRESHOLD 5     // Battery cutoff after 5 seconds

Trigger triggers[TRIGGER_COUNT] = {
    {TRIGGER_MAINS, false, 0},
    {TRIGGER_BATTERY, false, 0},
    {TRIGGER_DOOR, false, 0},
    {TRIGGER_TEMPERATURE, false, 0}
};

const char *TriggerNames[TRIGGER_COUNT] = {
    "Mains",
    "Battery",
    "Door",
    "Temperature"
};

void trigger_raise(TriggerTypes type, uint16_t current_ticks)
{
    if (type < TRIGGER_COUNT) {
        if (!triggers[type].active) {
            triggers[type].active = true;
            triggers[type].duration = current_ticks;
        } else {
            uint16_t duration;
            if (current_ticks >= triggers[type].duration) {
                duration = current_ticks - triggers[type].duration;
            } else {
                duration = (UINT16_MAX - triggers[type].duration) + current_ticks + 1;
            }
            triggers[type].duration = duration;
        }
    }
}

void trigger_lower(TriggerTypes type)
{
    if (type < TRIGGER_COUNT) {
        triggers[type].active = false;
        triggers[type].duration = 0;
    }
}

bool trigger_is_active(TriggerTypes type)
{
    if (type < TRIGGER_COUNT) {
        return triggers[type].active;
    }
    return false;
}

uint16_t trigger_get_duration(TriggerTypes type)
{
    if (type < TRIGGER_COUNT) {
        return triggers[type].duration;
    }
    return 0;
}

void trigger_clear_all(void)
{
    for (int i = 0; i < TRIGGER_COUNT; i++) {
        triggers[i].active = false;
        triggers[i].duration = 0;
    }
}

bool trigger_should_alarm(TriggerTypes type)
{
    if (type >= TRIGGER_COUNT || !triggers[type].active) {
        return false;
    }
    
    switch (type) {
        case TRIGGER_MAINS:
        case TRIGGER_TEMPERATURE:
            // Immediate triggers - alarm as soon as they're active
            return true;
            
        case TRIGGER_BATTERY:
            // Battery cutoff after threshold
            return triggers[type].duration >= TRIGGER_BATTERY_THRESHOLD;
            
        case TRIGGER_DOOR:
            // Door alarm after threshold
            return triggers[type].duration >= TRIGGER_DOOR_THRESHOLD;
            
        default:
            return false;
    }
}