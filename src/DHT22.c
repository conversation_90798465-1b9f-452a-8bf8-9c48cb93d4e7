/*
 * DHT22.c
 *
 * Created: 2022/07/29 03:26:05 PM
 *  Author: <PERSON><PERSON><PERSON>
 * 
  The output given out by the data pin will be in the order of:- 
    8bit humidity integer data
  + 8bit the Humidity decimal data +8 bit temperature integer data 
  + 8bit fractional temperature data 
  + 8bit parity bit. 
  To request the DHT11 module to send these data the I/O pin has to be momentarily made low
   and then held high.
 */ 
 #include <avr/io.h>
 #include <stdlib.h>
 #include <stdio.h>
 #include "lcd.h"
 #include "delay.h"
 #include "usart.h"
#include "mqtt.h"
#include "timer.h"
#include "interrupts.h"
#include "console.h"

 #define DHT11_PIN 4
 
#if 1
 void Request()				/* Micro controller send start pulse/request */
 {
	 DDRB |= (1<<DHT11_PIN);
	 PORTB &= ~(1<<DHT11_PIN);	/* set to low pin		*/
	delay_ms_1(20);				/* wait for 20ms		*/
	 PORTB |= (1<<DHT11_PIN);	/* set to high pin	*/
 }
  /*-----------------------------------------------------------
  Function   : Response()
  Purpose    : Basically checks for a low (80us), then high (80us) bit 
					response to start bit. Thereafter data will be sent.
  Parameters : 
  Returns    : 
  Notes      : 70us voltage-length means 1bit data "1"
				 :	Writing a 0 in the pin position in the DDRx will configure that 
				 :	pin as an input pin.
------------------------------------------------------------*/ 
 UI_8 Response(void)				/* receive response from DHT11 */
 {
	 Timer1_init(4) ;				/* Start timeout 4 sec */
	 asm("sei");

	 DDRB &= ~(1<<DHT11_PIN);
	 while(PINB & (1<<DHT11_PIN))
	 {
		if (intrflags.timer1_intr == 1)
		{
			Timer1_disable() ;				/* Disable Timer1 Interrupt*/
			intrflags.timer1_intr = 0 ;
			return (SYS_FAIL);
		}
	 }
	 while((PINB & (1<<DHT11_PIN))==0)
	 {
		if (intrflags.timer1_intr == 1)
		{
			Timer1_disable() ;				/* Disable Timer1 Interrupt*/
			intrflags.timer1_intr = 0 ;
			return (SYS_FAIL);
		}
	 }
	 while(PINB & (1<<DHT11_PIN))
	 {
		if (intrflags.timer1_intr == 1)
		{
			Timer1_disable() ;				/* Disable Timer1 Interrupt*/
			intrflags.timer1_intr = 0 ;
			return (SYS_FAIL);
		}
	 }	 
	 return (SYS_PASS);
 }

 /*-----------------------------------------------------------
  Function   : Receive_data()
  Purpose    : Checks for high or low bit in serial stream and
				 : packs into 8 bit value.
  Parameters : 
  Returns    : 8 bit value.
  Notes      : 70us voltage-length means 1bit data "1".
					Msb sent out first.
------------------------------------------------------------*/ 
 uint8_t Receive_data()			/* receive data */
{
	 uint8_t c = 0 ;

	 Timer1_init(4) ;				/* Start timeout 4 sec */
	 asm("sei");

	 for (int q=0; q<8; q++)
	 {
		 while((PINB & (1<<DHT11_PIN)) == 0)  /* check received bit 0 or 1 */
		 {			 
			 if (intrflags.timer1_intr == 1)
			 {
				 Timer1_disable() ;				/* Disable Timer1 Interrupt*/
				 intrflags.timer1_intr = 0 ;
				 return (SYS_FAIL);
			 }
		 }
		 _delay_us(30);
		 if(PINB & (1<<DHT11_PIN))		/* if high pulse is greater than 30ms */
			c = (c<<1)|(0x01);			/* then its logic HIGH */
		 else									/* otherwise its logic LOW */
			c = (c<<1);
		 while(PINB & (1<<DHT11_PIN))
		 {
			if (intrflags.timer1_intr == 1)
			{
				Timer1_disable() ;				/* Disable Timer1 Interrupt*/
				intrflags.timer1_intr = 0 ;
				return (SYS_FAIL);
			}
		 }
	 }
	 return c;
 }
 /*-----------------------------------------------------------
  Function   : DHT22_Meas()
  Purpose    : Main function to do measurement.
  Parameters : 
  Returns    : 
  Notes      : 
------------------------------------------------------------*/ 
UI_8 DHT22_Meas(mqtt_data_struct *mqtt_dataptr)
{
	char data[5];
	UI_8 checzum = 0, polarity = 0 ;
	UI_8 I_RH= 0, D_RH= 0, I_Temp= 0, D_Temp= 0, CheckSum= 0;
	//UI_8 tol_int = 0, tol_dec = 0 ;
	UI_16 tval = 0, integer = 0, decimal = 0 ;
	//UI_16 tolerance = 0 ;

	//while (1)
	//{
		console_writeline("\n\n\rStart DHT22");	
		Request();			/* send start pulse */
		if (Response() == SYS_FAIL)
		{
			return (SYS_FAIL);
		} 
		else
		{		 
			/* Response receive
			--------------------- */
			I_RH = Receive_data();	/* store first eight bit in I_RH */			 
			D_RH = Receive_data();	/* store next eight bit in D_RH */		 
			I_Temp = Receive_data();	/* store next eight bit in I_Temp */		 
			D_Temp = Receive_data();	/* store next eight bit in D_Temp */		 
			CheckSum = Receive_data();/* store next eight bit in CheckSum */			
	
			#if 1
			console_writeline("\n\rDHT22Humdity:");
			console_writeline("\n\r");
			USART1_Tx_UI_8_dec(I_RH) ;
			USART1_Tx_UI_8_hex(I_RH) ;
			console_writeline("\n\r");
			USART1_Tx_UI_8_dec(D_RH) ;
			USART1_Tx_UI_8_hex(D_RH) ;
			//console_writeline("\n\r");
			console_writeline("\n\rDHT22Temp:\n\r");
			USART1_Tx_UI_8_dec(I_Temp) ;
			console_writeline("\n\r");
			USART1_Tx_UI_8_dec(D_Temp) ;
			//console_writeline("\n\r");
			console_writeline("\n\rDHT22CheckZum: ");
			USART1_Tx_UI_8_dec(CheckSum) ;
			USART1_Tx_UI_8_hex(CheckSum) ;
			#endif
			checzum = (I_RH + D_RH + I_Temp + D_Temp) ;
			if (checzum != CheckSum)
			{
				console_writeline("\n\rChecksum Error\n\r");
				console_writeline(" 0x");
				USART1_Tx_UI_8_hex(CheckSum) ;
			}
			else
			{
				console_writeline("\n\rChecksum passed\n\r");
			} 
			 /*------------------------
			   Humidity measurement:
			 -------------------------*/			
			/* Make 16bit value out of I_RH & D_RH */
			tval = I_RH << 8 ;
			tval = tval | D_RH ;
			if (tval > 10)
			{				
				integer = tval / 10 ;
				decimal = tval - (integer * 10) ;
				console_writeline("\n\rHumidity tval: ");			
				USART1_Tx_UI_16_dec(tval) ;
				console_writeline(" (");	
				itoa(integer,data,10);
				console_writeline(data);
				console_writeline(".");
				itoa(decimal,data,10);
				console_writeline(data);
				console_writeline("%");
				console_writeline(")\n\r");
			} 
			else
			{
				console_writeline("\n\rHumidity value is 0");
				integer = 0 ;
				decimal = D_RH ;
			}
			sprintf(mqtt_dataptr->Humidity_currstr, "+%d.%d",integer, decimal);
			
			/*------------------------
			  Temperature measurement:
			-------------------------*/			
			if (I_Temp & 0x80) /* Result is Positive temperature */
				polarity = 1 ;
			else
				polarity = 0 ;

			tval = I_Temp << 8 ;
			tval = tval | D_Temp ;
			tval = tval & 0x7FFF ;	/*	Mask out polarity bit*/
			//tolerance = tval * 4 ;
			console_writeline("\n\rtemperature tval: ");
			USART1_Tx_UI_8_dec(tval) ;
			//if ((tolerance % 1000) != 0) 
			//{
				//tol_int = tolerance/1000 ;
				//tol_dec = tolerance % 1000 ;
			//} 
			//else if ((tolerance % 100) != 0)
			//{
				//tol_int = tolerance/100 ;
				//tol_dec = tolerance % 100 ;
			//}
			//else if ((tolerance % 10) != 0)
			//{
				//tol_dec = tolerance % 10 ;
			//}
			//else
			//{
				//tol_int = 0 ;
				//tol_dec = 0 ;
			//}
			//I_Temp += tol_int ;
			//D_Temp += tol_dec ;
			//tval = I_Temp << 8 ;
			//tval = tval | D_Temp ;
			
			integer = tval / 10 ;			
			//console_writeline("\n\rinteger val: ");
			//USART1_Tx_UI_8_hex(integer) ;
			decimal = tval - (integer * 10) ;
			//console_writeline("\n\rdecimal val: ");
			//USART1_Tx_UI_8_hex(decimal) ;

			mqtt_dataptr->Temp_Curr_int = integer ;
			mqtt_dataptr->Temp_Curr_dec = decimal ;
			//console_writeline("\n\rTemp_Curr_int: ");
			//USART1_Tx_UI_8_dec(mqtt_dataptr->Temp_Curr_int) ;			
			//console_writeline("\n\rTemp_Curr_dec: ");
			//USART1_Tx_UI_8_dec(mqtt_dataptr->Temp_Curr_dec) ;
			console_writeline("\n\rTemperature:");
			itoa(integer,data,10);
			console_writeline(data);
			console_writeline(".");
			itoa(decimal,data,10);
			console_writeline(data);
			console_writeline(" C");
			//console_writeline("\n\r");
			if (!polarity) /* Result is Positive temperature */
			{				
				sprintf(mqtt_dataptr->temp_currstr, "+%d.%d",integer, decimal);
			}
			else if (I_Temp & 0x80) /* Result is Negative temperature */
			{
				sprintf(mqtt_dataptr->temp_currstr, "-%d.%d",integer, decimal);
			}					
		}
		#if 1
		console_writeline("\n\rCurrHumidity: ");
		console_writeline(mqtt_dataptr->Humidity_currstr) ;
		console_writeline("\n\rCurrTemp: ");
		console_writeline(mqtt_dataptr->temp_currstr) ;
		#endif
		console_writeline("\n\n\r");
		delay_ms_1(3000);	
	//}
	return (SYS_PASS);
 }
 #endif
/*---------------------------------------------------------------------------------------- */
#if 0
  void Request()				/* Microcontroller send start pulse/request */
  {
	  DDRB |= (1<<DHT11_PIN);
	  PORTB &= ~(1<<DHT11_PIN);	/* set to low pin */
	 delay_ms_1(20);			/* wait for 20ms */
	  PORTB |= (1<<DHT11_PIN);	/* set to high pin */
  }

  void Response()				/* receive response from DHT11 */
  {
	  DDRB &= ~(1<<DHT11_PIN);
	  while(PINB & (1<<DHT11_PIN));
	  while((PINB & (1<<DHT11_PIN))==0);
	  while(PINB & (1<<DHT11_PIN));
  }

  uint8_t Receive_data()			/* receive data */
  {
	  for (int q=0; q<8; q++)
	  {
		  while((PINB & (1<<DHT11_PIN)) == 0);  /* check received bit 0 or 1 */
		  _delay_us(30);
		  if(PINB & (1<<DHT11_PIN))/* if high pulse is greater than 30ms */
		  c = (c<<1)|(0x01);	/* then its logic HIGH */
		  else			/* otherwise its logic LOW */
		  c = (c<<1);
		  while(PINB & (1<<DHT11_PIN));
	  }
	  return c;
  }

UI_8 DHT22_Meas(mqtt_data_struct *mqtt_dataptr)
{
	  char data[5];
	  UI_8 checzum = 0 ;
	  UI_8 tval = 0, tolerance = 0, integer = 0, decimal = 0 ;
	  	  
	  console_writeline("\n\rDHT22Temp: ");
	  
	  while(1)
	  {
		  Request();		/* send start pulse */
		  Response();		/* receive response */
		  I_RH=Receive_data();	/* store first eight bit in I_RH */
		  D_RH=Receive_data();	/* store next eight bit in D_RH */
		  I_Temp=Receive_data();	/* store next eight bit in I_Temp */
		  D_Temp=Receive_data();	/* store next eight bit in D_Temp */
		  CheckSum=Receive_data();/* store next eight bit in CheckSum */
		  
		  //if ((I_RH + D_RH + I_Temp + D_Temp) != CheckSum)
		  //{
			  //console_writeline("Error");
		  //}
		  //else
		  //{
			  console_writeline("\n\rHumidity:\n\r");
			  itoa(I_RH,data,10);
			  console_writeline(data);
			  console_writeline(".");			  
			  itoa(D_RH,data,10);
			  console_writeline(data);
			  console_writeline("%");
			  console_writeline("\n\r");

			  console_writeline("\n\rTemperature:\n\r");
			  itoa(I_Temp,data,10);
			  console_writeline(data);
			  console_writeline(".");			  
			  itoa(D_Temp,data,10);
			  console_writeline(data);
			  USART1_Tx_UI_8_dec(0xDF);
			  console_writeline("C ");

			  console_writeline("\n\rChecksaum:\n\r");
			  itoa(CheckSum,data,10);
			  console_writeline(data);
			  console_writeline(" ");

			   console_writeline("\n\r");
			   USART1_Tx_UI_8_dec(I_RH) ;
			   USART1_Tx_UI_8_hex(I_RH) ;
			   console_writeline("\n\r");
			   USART1_Tx_UI_8_dec(D_RH) ;
			   USART1_Tx_UI_8_hex(D_RH) ;
			   console_writeline("\n\r");
			   USART1_Tx_UI_8_dec(I_Temp) ;
			   console_writeline("\n\r");
			   USART1_Tx_UI_8_dec(D_Temp) ;
			   console_writeline("\n\r");
			   USART1_Tx_UI_8_dec(CheckSum) ;
			   USART1_Tx_UI_8_hex(CheckSum) ;

				tval = D_Temp  ;
				tolerance = tval/40 ;
				integer = (tval + tolerance)/10 ;
				decimal = tval - (integer * 10) ;
				mqtt_dataptr->Temp_Curr_int = integer ;
				mqtt_dataptr->Temp_Curr_dec = decimal ;
				console_writeline("\n\rTemperature:");
				itoa(integer,data,10);
				console_writeline(data);
				console_writeline(".");
				itoa(decimal,data,10);
				console_writeline(data);
				console_writeline("%");
				console_writeline("\n\r");

			   console_writeline("\n\n\r");
		  //}
		 delay_ms_1(2000);
	  }
  }
  #endif