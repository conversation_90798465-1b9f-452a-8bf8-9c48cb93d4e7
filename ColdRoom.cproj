﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" ToolsVersion="14.0">
  <PropertyGroup>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectVersion>7.0</ProjectVersion>
    <ToolchainName>com.Atmel.AVRGCC8.C</ToolchainName>
    <ProjectGuid>dce6c7e3-ee26-4d79-826b-08594b9ad897</ProjectGuid>
    <avrdevice>ATmega1284</avrdevice>
    <avrdeviceseries>none</avrdeviceseries>
    <OutputType>Executable</OutputType>
    <Language>C</Language>
    <OutputFileName>$(MSBuildProjectName)</OutputFileName>
    <OutputFileExtension>.elf</OutputFileExtension>
    <OutputDirectory>$(MSBuildProjectDirectory)\$(Configuration)</OutputDirectory>
    <AssemblyName>ColdRoom</AssemblyName>
    <Name>ColdRoom</Name>
    <RootNamespace>ColdRoom</RootNamespace>
    <ToolchainFlavour>Native</ToolchainFlavour>
    <KeepTimersRunning>true</KeepTimersRunning>
    <OverrideVtor>false</OverrideVtor>
    <CacheFlash>true</CacheFlash>
    <ProgFlashFromRam>true</ProgFlashFromRam>
    <RamSnippetAddress>0x20000000</RamSnippetAddress>
    <UncachedRange />
    <preserveEEPROM>true</preserveEEPROM>
    <OverrideVtorValue>exception_table</OverrideVtorValue>
    <BootSegment>2</BootSegment>
    <ResetRule>0</ResetRule>
    <eraseonlaunchrule>0</eraseonlaunchrule>
    <EraseKey />
    <AsfFrameworkConfig>
      <framework-data xmlns="">
        <options />
        <configurations />
        <files />
        <documentation help="" />
        <offline-documentation help="" />
        <dependencies>
          <content-extension eid="atmel.asf" uuidref="Atmel.ASF" version="3.52.0" />
        </dependencies>
      </framework-data>
    </AsfFrameworkConfig>
    <avrtool>com.atmel.avrdbg.tool.ispmk2</avrtool>
    <avrtoolserialnumber>0000B0026998</avrtoolserialnumber>
    <avrdeviceexpectedsignature>0x1E9706</avrdeviceexpectedsignature>
    <com_atmel_avrdbg_tool_simulator>
      <ToolOptions>
        <InterfaceProperties>
        </InterfaceProperties>
        <InterfaceName>
        </InterfaceName>
      </ToolOptions>
      <ToolType>com.atmel.avrdbg.tool.simulator</ToolType>
      <ToolNumber>
      </ToolNumber>
      <ToolName>Simulator</ToolName>
    </com_atmel_avrdbg_tool_simulator>
    <avrtoolinterface>ISP</avrtoolinterface>
    <avrtoolinterfaceclock>125000</avrtoolinterfaceclock>
    <com_atmel_avrdbg_tool_ispmk2>
      <ToolOptions>
        <InterfaceProperties>
          <IspClock>125000</IspClock>
        </InterfaceProperties>
        <InterfaceName>ISP</InterfaceName>
      </ToolOptions>
      <ToolType>com.atmel.avrdbg.tool.ispmk2</ToolType>
      <ToolNumber>0000B0026998</ToolNumber>
      <ToolName>AVRISP mkII</ToolName>
    </com_atmel_avrdbg_tool_ispmk2>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
    <ToolchainSettings>
      <AvrGcc>
        <avrgcc.common.Device>-mmcu=atmega1284 -B "%24(PackRepoDir)\atmel\ATmega_DFP\1.7.374\gcc\dev\atmega1284"</avrgcc.common.Device>
        <avrgcc.common.optimization.RelaxBranches>True</avrgcc.common.optimization.RelaxBranches>
        <avrgcc.common.outputfiles.hex>True</avrgcc.common.outputfiles.hex>
        <avrgcc.common.outputfiles.lss>True</avrgcc.common.outputfiles.lss>
        <avrgcc.common.outputfiles.eep>True</avrgcc.common.outputfiles.eep>
        <avrgcc.common.outputfiles.srec>True</avrgcc.common.outputfiles.srec>
        <avrgcc.common.outputfiles.usersignatures>False</avrgcc.common.outputfiles.usersignatures>
        <avrgcc.compiler.general.ChangeDefaultCharTypeUnsigned>True</avrgcc.compiler.general.ChangeDefaultCharTypeUnsigned>
        <avrgcc.compiler.general.ChangeDefaultBitFieldUnsigned>True</avrgcc.compiler.general.ChangeDefaultBitFieldUnsigned>
        <avrgcc.compiler.symbols.DefSymbols>
          <ListValues>
            <Value>NDEBUG</Value>
          </ListValues>
        </avrgcc.compiler.symbols.DefSymbols>
        <avrgcc.compiler.directories.IncludePaths>
          <ListValues>
            <Value>%24(PackRepoDir)\atmel\ATmega_DFP\1.7.374\include\</Value>
          </ListValues>
        </avrgcc.compiler.directories.IncludePaths>
        <avrgcc.compiler.optimization.level>Optimize for size (-Os)</avrgcc.compiler.optimization.level>
        <avrgcc.compiler.optimization.PackStructureMembers>True</avrgcc.compiler.optimization.PackStructureMembers>
        <avrgcc.compiler.optimization.AllocateBytesNeededForEnum>True</avrgcc.compiler.optimization.AllocateBytesNeededForEnum>
        <avrgcc.compiler.warnings.AllWarnings>True</avrgcc.compiler.warnings.AllWarnings>
        <avrgcc.linker.libraries.Libraries>
          <ListValues>
            <Value>libm</Value>
          </ListValues>
        </avrgcc.linker.libraries.Libraries>
        <avrgcc.assembler.general.IncludePaths>
          <ListValues>
            <Value>%24(PackRepoDir)\atmel\ATmega_DFP\1.7.374\include\</Value>
          </ListValues>
        </avrgcc.assembler.general.IncludePaths>
      </AvrGcc>
    </ToolchainSettings>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)' == 'Debug' ">
    <ToolchainSettings>
      <AvrGcc>
        <avrgcc.common.Device>-mmcu=atmega1284 -B "%24(PackRepoDir)\atmel\ATmega_DFP\1.7.374\gcc\dev\atmega1284"</avrgcc.common.Device>
        <avrgcc.common.optimization.RelaxBranches>True</avrgcc.common.optimization.RelaxBranches>
        <avrgcc.common.outputfiles.hex>True</avrgcc.common.outputfiles.hex>
        <avrgcc.common.outputfiles.lss>True</avrgcc.common.outputfiles.lss>
        <avrgcc.common.outputfiles.eep>True</avrgcc.common.outputfiles.eep>
        <avrgcc.common.outputfiles.srec>True</avrgcc.common.outputfiles.srec>
        <avrgcc.common.outputfiles.usersignatures>False</avrgcc.common.outputfiles.usersignatures>
        <avrgcc.compiler.general.ChangeDefaultCharTypeUnsigned>True</avrgcc.compiler.general.ChangeDefaultCharTypeUnsigned>
        <avrgcc.compiler.general.ChangeDefaultBitFieldUnsigned>True</avrgcc.compiler.general.ChangeDefaultBitFieldUnsigned>
        <avrgcc.compiler.symbols.DefSymbols>
          <ListValues>
            <Value>DEBUG</Value>
          </ListValues>
        </avrgcc.compiler.symbols.DefSymbols>
        <avrgcc.compiler.directories.IncludePaths>
          <ListValues>
            <Value>%24(PackRepoDir)\atmel\ATmega_DFP\1.7.374\include\</Value>
          </ListValues>
        </avrgcc.compiler.directories.IncludePaths>
        <avrgcc.compiler.optimization.level>Optimize debugging experience (-Og)</avrgcc.compiler.optimization.level>
        <avrgcc.compiler.optimization.PackStructureMembers>True</avrgcc.compiler.optimization.PackStructureMembers>
        <avrgcc.compiler.optimization.AllocateBytesNeededForEnum>True</avrgcc.compiler.optimization.AllocateBytesNeededForEnum>
        <avrgcc.compiler.optimization.DebugLevel>Default (-g2)</avrgcc.compiler.optimization.DebugLevel>
        <avrgcc.compiler.warnings.AllWarnings>True</avrgcc.compiler.warnings.AllWarnings>
        <avrgcc.linker.libraries.Libraries>
          <ListValues>
            <Value>libm</Value>
          </ListValues>
        </avrgcc.linker.libraries.Libraries>
        <avrgcc.assembler.general.IncludePaths>
          <ListValues>
            <Value>%24(PackRepoDir)\atmel\ATmega_DFP\1.7.374\include\</Value>
          </ListValues>
        </avrgcc.assembler.general.IncludePaths>
        <avrgcc.assembler.debugging.DebugLevel>Default (-Wa,-g)</avrgcc.assembler.debugging.DebugLevel>
      </AvrGcc>
    </ToolchainSettings>
  </PropertyGroup>
  <ItemGroup>
    <Compile Include="adc.c">
      <SubType>compile</SubType>
    </Compile>
    <Compile Include="adc.h">
      <SubType>compile</SubType>
    </Compile>
    <Compile Include="config.c">
      <SubType>compile</SubType>
    </Compile>
    <Compile Include="config.h">
      <SubType>compile</SubType>
    </Compile>
    <Compile Include="console.c">
      <SubType>compile</SubType>
    </Compile>
    <Compile Include="console.h">
      <SubType>compile</SubType>
    </Compile>
    <Compile Include="delay.c">
      <SubType>compile</SubType>
    </Compile>
    <Compile Include="delay.h">
      <SubType>compile</SubType>
    </Compile>
    <Compile Include="door.c">
      <SubType>compile</SubType>
    </Compile>
    <Compile Include="door.h">
      <SubType>compile</SubType>
    </Compile>
    <Compile Include="e2prom.c">
      <SubType>compile</SubType>
    </Compile>
    <Compile Include="e2prom.h">
      <SubType>compile</SubType>
    </Compile>
    <Compile Include="hardware.c">
      <SubType>compile</SubType>
    </Compile>
    <Compile Include="hardware.h">
      <SubType>compile</SubType>
    </Compile>
    <Compile Include="hexdump.c">
      <SubType>compile</SubType>
    </Compile>
    <Compile Include="hexdump.h">
      <SubType>compile</SubType>
    </Compile>
    <Compile Include="io_pins.c">
      <SubType>compile</SubType>
    </Compile>
    <Compile Include="io_pins.h">
      <SubType>compile</SubType>
    </Compile>
    <Compile Include="lcd.c">
      <SubType>compile</SubType>
    </Compile>
    <Compile Include="lcd.h">
      <SubType>compile</SubType>
    </Compile>
    <Compile Include="lcd_def.h">
      <SubType>compile</SubType>
    </Compile>
    <Compile Include="main.c">
      <SubType>compile</SubType>
    </Compile>
    <Compile Include="modem.c">
      <SubType>compile</SubType>
    </Compile>
    <Compile Include="modem.h">
      <SubType>compile</SubType>
    </Compile>
    <Compile Include="Modem_def.h">
      <SubType>compile</SubType>
    </Compile>
    <Compile Include="mqtt.c">
      <SubType>compile</SubType>
    </Compile>
    <Compile Include="mqtt.h">
      <SubType>compile</SubType>
    </Compile>
    <Compile Include="mqtt_conf.h">
      <SubType>compile</SubType>
    </Compile>
    <Compile Include="OW_DS1820.c">
      <SubType>compile</SubType>
    </Compile>
    <Compile Include="OW_DS1820.h">
      <SubType>compile</SubType>
    </Compile>
    <Compile Include="pindef.h">
      <SubType>compile</SubType>
    </Compile>
    <Compile Include="pins.h">
      <SubType>compile</SubType>
    </Compile>
    <Compile Include="power.c">
      <SubType>compile</SubType>
    </Compile>
    <Compile Include="power.h">
      <SubType>compile</SubType>
    </Compile>
    <Compile Include="state.c">
      <SubType>compile</SubType>
    </Compile>
    <Compile Include="state.h">
      <SubType>compile</SubType>
    </Compile>
    <Compile Include="system.h">
      <SubType>compile</SubType>
    </Compile>
    <Compile Include="temperature.c">
      <SubType>compile</SubType>
    </Compile>
    <Compile Include="temperature.h">
      <SubType>compile</SubType>
    </Compile>
    <Compile Include="timer.c">
      <SubType>compile</SubType>
    </Compile>
    <Compile Include="timer.h">
      <SubType>compile</SubType>
    </Compile>
    <Compile Include="trigger.c">
      <SubType>compile</SubType>
    </Compile>
    <Compile Include="trigger.h">
      <SubType>compile</SubType>
    </Compile>
    <Compile Include="twi.h">
      <SubType>compile</SubType>
    </Compile>
    <Compile Include="types.h">
      <SubType>compile</SubType>
    </Compile>
    <Compile Include="usart.c">
      <SubType>compile</SubType>
    </Compile>
    <Compile Include="usart.h">
      <SubType>compile</SubType>
    </Compile>
  </ItemGroup>
  <Import Project="$(AVRSTUDIO_EXE_PATH)\\Vs\\Compiler.targets" />
</Project>