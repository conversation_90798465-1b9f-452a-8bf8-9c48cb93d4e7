; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html
[env:ATmega1284]
platform = atmelavr
board = ATmega1284
board_build.f_cpu = 11059200L
upload_protocol = avrispmkii
monitor_port = /dev/ttyUSB0
monitor_speed = 115200
upload_command = avrdude -C ${platformio.packages_dir}/tool-avrdude/avrdude.conf -v -p atmega1284 -c avrispmkii -P usb -B 4 -U flash:w:$SOURCE:i