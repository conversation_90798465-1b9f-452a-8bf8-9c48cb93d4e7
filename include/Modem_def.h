/*
 * Modem_def.h
 *
 * Created: 2018/10/10 08:52:15 PM
 *  Author: <PERSON><PERSON><PERSON>
 */ 


#ifndef MODEM_DEF_H_
#define MODEM_DEF_H_

#define SETBIT(ADDRESS,BIT) (ADDRESS |= (1<<BIT))
#define CLEARBIT(ADDRESS,BIT) (ADDRESS &= ~(1<<BIT))

/* Macro for testing of a single bit in an I/O location*/
#define CHECKBIT(ADDRESS,BIT) (ADDRESS & (1<<BIT))

/* Example of usage*/
if(CHECKBIT(PORTD,PIND1)) /* Test if PIN 1 is set*/
{
	CLEARBIT(PORTD,PIND1); /* Clear PIN 1 on PORTD*/
}
if(!(CHECKBIT(PORTD,PIND1))) /* Test if PIN 1 is cleared*/
{
	SETBIT(PORTD,PIND1); /* Set PIN 1 on PORTD*/
}



#endif /* MODEM_DEF_H_ */