/*************************************************************************
* Title:    Usart.h
------------------------------------------------------------------------------
Company         : TXD Systems
Module          : Project USART driver defines
Purpose         :
Notes           :
History         :
Date			: 24/03/2018
dd/mm/yyyy  Author   Description
----------  ------   -----------
------------------------------------------------------------------------------*/
#ifndef USART_H
	#define USART_H

#include "types.h"
#include "system.h"

/* External functions called:*/
extern void usart0_init( uint8_t ubrr);
extern void usart0_transmit( unsigned char data );
extern unsigned char usart0_receive( void );
extern void usart0_flush( void );
extern void usart0_send_string(char *str) ;
extern void usart0_rx_disable(void) ;
extern void usart0_rx_enable(void) ;

extern void usart1_init( uint8_t ubrr);
extern void usart1_send_char( char data );
extern void usart1_send_raw_string(char *str) ;

extern void usart0_reset_buffer(void);
extern uint8_t usart0_get_buffer_size(void);
extern void usart0_hex_dump_buffer(void);
extern void usart0_pack_data(void);
extern bool usart0_read_data(char *command, char *result, char *data, uint8_t dataSize);
extern bool usart0_parse_data_balance(void);
extern bool usart0_send_command(char *command, char *expected);
extern uint8_t usart0_get_unsolicited_data(char *buffer, uint16_t maxSize);

#endif