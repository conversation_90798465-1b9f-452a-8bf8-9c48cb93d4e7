/*************************************************************************
* Title:    Usart.h
------------------------------------------------------------------------------
Company         : TXD Systems
Module          : Project USART driver defines
Purpose         :
Notes           :
History         :
Date			: 24/03/2018
dd/mm/yyyy  Author   Description
----------  ------   -----------
------------------------------------------------------------------------------*/
#ifndef USART_H
	#define USART_H

#include "types.h"
#include "system.h"

/* External functions called:*/
extern void USART0_Init( UI_8 ubrr);
extern void USART0_Transmit( unsigned char data );
extern unsigned char USART0_Receive( void );
extern void USART0_Transmit_9bits( UI_8  data );
extern UI_8 USART0_Receive_9bit( void );
extern void USART0_Flush( void );
extern void USART_ERROR_HANDLER(void);
extern void USART0_sendINT( UI_8  data );
extern void USART0_SendString(char *str) ;
extern void USART0_RX_Disable(void) ;
extern void USART0_RX_Enable(void) ;

extern void USART1_Init( UI_8 ubrr);
extern void USART1_Tx_int_8_dec( int8_t val ) ;
extern void USART1_SendChar( char data );
extern void USART1_SendRawString(char *str) ;
extern void USART1_Tx_UI_8_dec( uint8_t val ) ;
extern void USART1_Tx_UI_16_dec( uint16_t val ) ;
extern void USART1_Tx_UI_16_hex( uint16_t val ) ;
extern void USART1_Tx_UI_8_hex( uint8_t val ) ;
extern void USART1_Tx_int_8_hex( int8_t val ) ;
extern void USART1_Tx_int_32_hex( int32_t val ) ;
extern void USART1_Tx_UI_8_bin( uint8_t val ) ;
extern void USART1_Tx_UI_16_bin( uint16_t val ) ;
extern void USART1_Tx_UI_32_dec( uint32_t val ) ;
extern void USART1_ClearBuffer(void);
extern UI_8 USART1_Wait4RxBuf2Fill (UI_8 cnt, UI_8 time_out);
extern UI_8 USART1_GetBufferSize(void);
extern void USART0_HexDumpBuffer(void);
extern void USART0_PackData(void);
extern UI_8 USART0_SendCommand(char *command, char *expected);
extern UI_8 USART0_ReadData(char *command, char *result, char *data, UI_8 dataSize);
extern UI_8 USART0_ParseDataBalance(void);
extern UI_8 USART0_GetUnsolicitedData(char *buffer, UI_16 maxSize);

extern UI_8  uart_transmitPacket (UI_8 *dataPtr, UI_16 dataSize);
extern UI_8  uart_receivePacket  (UI_8 *dataPtr, UI_16 *dataSize);

/* Global variable definitions:*/
extern volatile UI_8 UART_RxD;

#endif