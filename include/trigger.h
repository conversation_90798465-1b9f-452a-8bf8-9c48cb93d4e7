#ifndef TRIGGER_H
#define TRIGGER_H

#include "types.h"

typedef enum
{
    TRIGGER_MAINS,
    TRIGGER_BATTERY,
    TRIGGER_DOOR,
    TRIGGER_TEMPERATURE,
    TRIGGER_COUNT
} TriggerTypes;

typedef struct
{
    TriggerTypes type;
    bool active;
    uint16_t duration;
} Trigger;

extern Trigger triggers[TRIGGER_COUNT];
extern const char *TriggerNames[TRIGGER_COUNT];

void trigger_raise(TriggerTypes type, uint16_t current_ticks);
void trigger_lower(TriggerTypes type);
bool trigger_is_active(TriggerTypes type);
uint16_t trigger_get_duration(TriggerTypes type);
void trigger_clear_all(void);
bool trigger_should_alarm(TriggerTypes type);

#endif // TRIGGER_H