/*
 * Modem.h
 *
 * Created: 2018/07/14 10:55:36 AM
 *  Author: <PERSON><PERSON><PERSON>
 */ 


#ifndef MODEM_H_
#define MODEM_H_

//#include "nmea.h"
#include "mqtt.h"

/* Functions */
extern UI_8 ModemInit(void) ;
extern UI_8 GPS_Init(void);
extern void Modem_NMEA_stream_ON(void);
extern UI_8 SMS_Await_Incoming(UI_8 disply, UI_8 chars) ;
//extern UI_8 SMS_indexGet(char strng[], char message[MSGLNGHT]) ;
extern UI_8 SMS_Get_Message(char *message, char info[CELLNR]);
extern UI_8 Modem_Pwr_ON(void) ;
extern void Modem_Pwr_OFF(void) ;
extern void Modem_Pwr_Restart(void) ;
extern UI_8 Send_AT_Cmd(mqtt_data_struct *mqtt_dataptr) ;
//extern UI_8 GPS_Location(nmea_struct *nmea) ;
extern UI_8 Status_Pin_Check (void) ;
extern void Error_Disply(char *dsplymsg, UI_8 row) ;
//extern void Send_RQF_GPS_pos(void) ;
//extern void NMEAD_display(void) ;
//extern void NMEA_parse(nmea_struct *nmeaptr) ;
extern UI_8 Wait_for_OK(void) ;
extern char * Check4string(char strng[]) ;
//extern UI_8 Wait_for_String(char strng[], UI_8 length) ;
extern const char *Modem_IMEI(void);
extern const char *Modem_ICCID(void);
extern void LCD_long_display(char *data) ;
extern UI_8 Wait_for_char(char ch) ;
extern UI_8 Modem_USSD_GetDataBalance();
extern  UI_8 FindSigQ(mqtt_data_struct *mqtt_dataptr) ;
//extern void SendSMS(struct nmea_struct nmea) ;
extern UI_8 Wait4RxBuf2Fill (UI_8 cnt, UI_8 time_out) ;
extern char * Check4strArray(char Rxbuf[], char *strng) ;

/* Variables */
//extern UI_8 intrflags.timer1_intr ;

#endif /* MODEM_H_ */
