/*
 * mqtt.h
 *
 * Created: 2020/01/04 09:28:39 PM
 *  Author: Bronko
 */
#include "system.h"
#include "config.h"

#ifndef MQTT_H_
#define MQTT_H_

// typedef struct mqtt_IMEI_struct
//{
// char IMEI[16];	// 867556040015946 - 15 + EOL
// char ICCID[21];	// 8927000006881831328f - 20 + EOL
//} mqtt_IMEA_struct ;
/*Struct size = 157
---------------------*/
typedef struct
{
	char IMEI[1];		// 867556040015946 - 15 + EOL
	char ICCID[1];	// 8927000006881831328f - 20 + EOL
	char unit_id[1]; // FRIDGE-1 - 8 charc
	int16_t TempMax;				// Temperature limit Values will be set in the LCD menu's
	int16_t TempMin;
	UI_8 Polarity; // Sw NC/NO setting

	int8_t Temp_Curr_int; // Whole part
	int8_t Temp_Curr_dec; // Decimal part
	int8_t Temp_Prev_int;
	int8_t Temp_Prev_dec;
	char temp_currstr[8];
	char RTD_Temperature_str[8];
	int8_t DS1820_Curr_int;
	int8_t DS1820_Curr_dec;
	int16_t DS1820_celcius;
	char DS1820_temp_currstr[5 + 1];
	char Humidity_currstr[8];
	char TempMaxStr[4];
	char TempMinStr[4];
	char rev_number[5]; // 02.02
	// char telegram_no[11] ;
	char SignalStrng[5];
	char ISP[9]; // Internet service provider
	UI_16 Batt_volt;
	char VBatt_str[7]; // VBATT (VBatt power on PL3)
	UI_16 Main_12Vin_PSU;
	char Main_12V_PSU_str[7]; // Main_12Vin_PSU (Aux power input to charge VBatt and power board) -K6
	UI_8 Door_alarm_trig;
	char door_alarm_time_hour[2];
	char door_alarm_time_min[3];
	UI_16 door_alarm_time;		   // values total nr of sec
	UI_8 volatile Update_Stat_reg; // can be changed by functions, stack var is not reliable
	UI_8 Prev_Stat_reg;
	UI_8 volatile Stat_reg_2;
} mqtt_data_struct;


 extern UI_8 MQTT_Init(void) ;
 extern UI_8 Wait_4_CONNECT_OK(void) ;
 extern UI_8 MQTT_Connect_Packet(void) ;
 extern UI_8 MQTT_Connect(mqtt_data_struct *mqtt_dataptr) ;
 extern UI_8 Wait_4_SrcStrng(char *str, UI_8 len) ;
 //extern UI_8 MQTT_Send(struct nmea_struct *nmea) ;
 extern UI_8 MQTT_Init_Connect(void) ;
 extern UI_8 MQTT_Subscribe(void) ;
 extern UI_8 MQTT_SubscribeTopic(char *) ;
 extern void MQTT_Unsubscribe(void) ;
 extern UI_8 MQTT_Subscribe_RetMsg(mqtt_data_struct *mqtt_dataptr);

extern bool ConfigPublish(void);
extern bool DataPublish(void);
extern bool TriggerPublish(void);

#endif /* MQTT_H_ */