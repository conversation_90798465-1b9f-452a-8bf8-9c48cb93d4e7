/*
 * lcd_def.h
 *
 * Created: 2018/04/26 10:58:19 PM
 *  Author: <PERSON><PERSON><PERSON>
 *
 * Note: The PCF8574  address i.e. 0x4E should be 0x27. It appears the address
 *			bits are left shifted 27H * 2 = 4EH. This needs to be investigated.
 */ 


#ifndef LCD_DEF_H_
#define LCD_DEF_H_

/*------------------------------------------------------------------------------
                           CONSTANT DEFINITIONS
------------------------------------------------------------------------------*/
#define LCD_TOTAL_COL             16
#define LCD_TOTAL_ROW             2

#define PCF8574  0x4E		/*0x27 - 0010 1110 MSB 1st = 0100 0111 (0x4E)*/
#define RS0_LCD_I2C 0xFE
#define RS1_LCD_I2C 0x01
#define EN0_LCD_I2C 0xFB
#define EN1_LCD_I2C 0x04
#define RD_LCD_I2C  0x02
#define WR0_LCD_I2C 0xFD
#define LCD_BACKLIGHT_ON 0x08
#define LCD_BACKLIGH_OFF 0xF7

// For parallel version
#define LCD_EN 0x01
#define LCD_WR 0x20
#define LCD_RS 0x40			/* TMS-2.0 */
//#define LCD_RS 0x10
#define bit_4 0x02
#define bit_8 0x03
#define RS_bit 4
#define RS_Data 1
#define RS_Cmd 0

#define CWR_LCD_I2C 0xF8	/*EN, R/W, RS == 0 */

/*------------------------------------------------------------------------------
                                GLOBAL DEFINITIONS
------------------------------------------------------------------------------*/
#define OFF  0 
#define ON   1 
#define RS0  0
#define RS1  1
/*------------------------------------------------------------------------------
                                      DEFINES
------------------------------------------------------------------------------*/
#define LCD_2LINE               0x3c
#define LCD_4LINE               0x09
#define LCD_ON                  0x0f
#define LCD_CLR                 0x01
#define LCD_MODE1               0x06
#define LCD_HOME                0x02
#define LCD_CURS_ON             0x0E
#define LCD_CURS_OFF            0x0D
#define LCD_BLINK_ON            0x0F
#define LCD_BLINK_OFF           0x0E
#define LCD_DISP_ON				0x0C
#define LCD_DISP_OFF            0x08
#define LCD_ALL_OFF             0x08

/*Row offsets in memory
-----------------------
Addressing:
1st. line 0x00..0x13
2nd. line 0x14..0x40
3rd. line 0x28..0x14
4th. line 0x3C..0x54
--------------------------------------*/
#define LCD_ROW1                0x00
#define LCD_ROW2                0x40
#define LCD_ROW3                0x14
#define LCD_ROW4                0x54

#define LCD_LINE_1 0
#define LCD_LINE_2 1
#define LCD_LINE_3 2
#define LCD_LINE_4 3


/* Backlight pin assignments */
#define BL_PORT                 PORTD
#define BL_PIN                  PIND0
//#define BL_DIR                  DDRD
//#define BL_DEF                  1     /* Backlight Default state*/

//#define DEBUG                  0     /* 1 - debug ON */
#define LCD_I2C_ADDR       0x40

//#define cmdTime 5
/*------------------------------------------------------------------------------
                                GLOBAL VARIABLES
------------------------------------------------------------------------------*/

#endif /* LCD_DEF_H_ */