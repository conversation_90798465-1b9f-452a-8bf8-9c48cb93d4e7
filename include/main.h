/*
 * main.h
 *
 * Created: 2020/02/29 07:33:47 PM
 *  Author: <PERSON><PERSON><PERSON>
 */ 


#ifndef MAIN_H_
#define MAIN_H_

#define SEC_10s 163835
#define SEC_5s 81918
#define SEC_4s 65534
#define SEC_2s 32768
#define SEC_1s 16384
#define SEC_0_5s 8192

#define NMEABAUD 4800
#define NMEAUBRR (FOSC/16/(NMEABAUD))-1
//#define ButPin	PINB0	//Pin to read the button press D8

/* Prototypes: */
void Error_Code_Display(UI_8 code) ;
void Check_JTAG(void);
//void i2c_scanner()
//void i2c_scanner();
//UI_8 get_ack_status(UI_8 address);

/* External functions:
----------------------*/
extern void USART0_Init( UI_8 ubrr);
extern volatile UI_8 UART_RxD;



/* Functions prototype:
----------------------*/
void Debug_RxIntr(void) ;
void Debug_Gothere(UI_8 nr);
void Timer1_Test(void);
void Show_StackPtr(void);



#endif /* MAIN_H_ */