/*************************************************************************
* Title:    system.h
-----------------------------------------------------------------------------
Company         : TXD Systems
Module          : Project generic defines
Purpose         :  
Notes           :
History         :
Date			: 24/03/2018
dd/mm/yyyy  Revision   Description
----------  ---------   -----------
15/5/21					
------------------------------------------------------------------------------*/
#ifndef SYSTEM_H
#define SYSTEM_H

//#include <avr/io.h>
#include <avr/interrupt.h>
#include "types.h"

/*------------------------------------------------------------------------------
                           CONSTANT DEFINITIONS
------------------------------------------------------------------------------*/
 /* Revision changed here
 ------------------------*/
#define VERS "4.30"	//Set Version Number

#define _DS1820_DISPLY_

#define LED_D1			PORTD4

#define LCD_SW			PORTC7

#define BATT_CUTOFF_PIN  PORTA4

#define _RATIO_10_2K2_		/* for R1=10k, R2=2k2*/

#ifdef _RATIO_10_2K2_ /*
7	   10	2.2	2.5	409.6	0.*********	1.*********	517.0360656
6.8	10	2.2	2.5	409.6	0.*********	1.226229508	502.2636066
6.6	10	2.2	2.5	409.6	0.*********	1.190163934	487.4911475
6.5	10	2.2	2.5	409.6	0.*********	1.172131148	480.104918
6.4	10	2.2	2.5	409.6	0.*********	1.154098361	472.7186885
*/
	#define VBATLOWCUT_7V2  531
	#define VBATLOWCUT_7V1  524
	#define VBATLOWCUT_6V8  502
	#define VBATLOWCUT_6V6  487
	#define VBATLOWCUT_6V5  480
	#define VBATLOWCUT_6V4  472

	// #define VBATT_LOW_LIMT  VBATLOWCUT_6V8
	#define VBATT_LOW_LIMT   68	// 6.8V

/* 490 = 6.8 Volt	
12	   10	2.2	2.5	409.6	0.*********	2.163934426	886.347541
11.9	10	2.2	2.5	409.6	0.*********	2.145901639	878.9613115
11.8	10	2.2	2.5	409.6	0.*********	2.127868852	871.575082
11.7	10	2.2	2.5	409.6	0.*********	2.109836066	864.1888525
11.6	10	2.2	2.5	409.6	0.*********	2.091803279	856.802623
11.5	10	2.2	2.5	409.6	0.*********	2.073770492	849.4163934*/

	#define MAIN_12VIN_LMT  1107	
	// #define MAIN_12VIN_MIN  850	/* 850 = 11.6 Volt (8.2****+0.3 = 11.8)*/
	// #define MAIN_9VMAIN_IN  665	/* 9V - if a main voltage is present*/
	#define MAIN_12VIN_MIN  116	/* 11.6V */
	#define MAIN_9VMAIN_IN   90	/* 9V */
#else
	#define VBATLOWCUT_7V0  595	/* for R1=10k, R2=2k7*/	
	#define VBATLOWCUT_6V8  578
	#define VBATLOWCUT_6V6  561
	#define VBATLOWCUT_6V5  553
	#define VBATLOWCUT_6V4  544
	#define VBATT_LOW_LIMT  578 ;		/* 578 = 6.8 Volt	*/

	#define MAIN_9VMAIN_IN  783
	#define MAIN_12VIN_LMT  986	/* 986 = 11.6 Volt (8.2**** = 11.5)*/
	#define MAIN_12VIN_MIN  692	/* 692 = 8.2 Volt */
#endif

#define MOSFET_1		PORTB2
#define MOSFET_2		PORTB3
#define DOORALRM		MOSFET_1
#define BATCUTOFF		MOSFET_2

/* EEPROM Addresses
-------------------*/
#define TELEGRAM_EEPROM		0x0000
#define ID_EEPROM			0x0016
#define NORM_OC_EEPROM		0x0026
#define TEMPMAX_EEPROM		0x0034
#define TEMPMIN_EEPROM		0x0035
#define TMPTRCTRL_EEPROM	0x0036
#define IMEI_EEPROM			0x0037
#define ICCID_EEPROM		0x0047
#define DOORH_EEPROM		0x005D
#define DOORM_EEPROM		0x005F
#define UNIT_ID_EEPROM		0x0062

#define EEPROM_DOOR_HOUR 0x0020
#define EEPROM_DOOR_MIN  0x0021

#define IMEI_LNGTH 15
#define ICCID_LNGTH 20
#define UNITID_LNGTH 7
#define REVISION_LNGTH 4
#define LOCATION_LNGTH 0
// #define CONF_CMP_SIZE 27
#define CONF_CMP_SIZE 64

/* Status reg set values 
-----------------------*/
#define UPDATED					7
#define TEMP_HI					6
#define TEMP_LO					5
#define VBATT_LO					4
#define VMAIN_LO					3
#define DOOR_ALARM				2
#define DOOR_PREV_OPEN			1
#define DOOR_STAT_OC				0		/* door open = 1, door closed = 0*/

/* Status_reg_1 Test values 
----------------------------*/
#define UPDATED_BIT			0x80
#define TEMP_HI_LMT_BIT		0x40
#define TEMP_LO_LMT_BIT		0x20
#define VBATT_LO_BIT		0x10
#define VMAIN_LO_BIT  		0x08
#define DOOR_ALARM_BIT		0x04
#define DOOR_PREV_OPEN_BIT  0x02
#define DOOR_OPENSTAT_BIT	0x01		/* 1 = Open, 0 = Closed */

#define TRIGGER_BITS			0x7C		/* 0111 1100 */
#define OTHER_TRIG_BITS		0x78		/* 0111 1000 */

//#define DOOR_OPEN_STATUS	0x03		/* 0000 0011 */

/* Status_reg_2 Test values
-----------------------------*/
#define SERV_DISCONNECTED_BIT	0x80 /* Set when server connection failed */
#define DISPY_DEF_SCRN_BIT		0x40	/* display default screen */
#define STARTUP_BIT				0x20	/* set bit for start up, cleared therafter */
#define WEBSRV_DIFF_BIT			0x10	/* Used when Config update has occurred from the Server*/
#define KEEP_DSPLY_NOCNF  		0x08	/* Allows "Config Not set" to be displayed a couple of sec's*/
#define STARTUP_CONF_BIT		0x04 
#define FIRST_CONF_BIT			0x02
#define TMPTR_CTRL_BIT			0x01	// to control external heater/cooler element

#define NORM_OPEN  1
#define NORM_CLOSE 0
#define NOT_SET 0
#define CLOSED 0

#define IO_PORTA 1
#define IO_PORTB 2
#define IO_PORTC 3
#define IO_PORTD 4

/* SIM800C pin assignments */
#define SIM800_STAT	PIND5
#define SIM800_PORT	IO_PORTD

#define ButPin	PINC2 //Button Pin PB1
#define ButPort IO_PORTC	//Pin to read the button press PC2
#define REPinB	PINC3	//Pin to read the button press PC3
#define REBpinPort IO_PORTC
#define REPinA	PINC4	//Pin to read the button press PC4
#define REApinPort IO_PORTC

//#define POWER_C		5
//#define SMILE_C		6
//#define HEARTFUL_C	7
#define SIG_1		0
#define SIG_2		1
#define SIG_3		2
#define SIG_4		3
#define SIG_5		4
#define SIG_6		5
#define SIG_7		6
#define CELCIUS_C	7

/* System status bit definitions:
  -------------------------------*/
#define WARN_TELGR_UNID		0x05
#define DSPLY_UNITID_WARN	0x06		/* 0110*/

#define NO_TELGRM				0x01	/* 1 = System EEPROM values have been configure*/
#define TEL_MSG_DSPLY_DONE 0x02 
#define NO_UNIT_ID			0x04 
//#define UNITID_DSPLY_DONE  0x08 
/*#define ??? 0x10
#define ??? 0x20
#define ??? 0x40*/
//#define EEPROM_RD_REQ	 0x80

#define NO_SIG_STRNGTH	0x80
#define NO_NETW_REG		0x40
#define NO_AT_RSPNS		0x20
#define SIM_NOT_INSERTED 0x10

#define SERVER_DISCONN	0x80
#define JUSTWORX			0x40
#define NO_GPRS			0x20
#define NO_MQTT			0x10	/* Wrong Modem, not MQTT configured*/

#define SIM868_PWRKEY	PORTD7

#define SYS_PASS       0
#define SYS_FAIL       1

#define DISPY       true
#define NO_DISPY    false

#define HEX 16
#define DEC 10
#define BIN 2
#define FAST 1
#define SLOW 0
#define HALT for(;;);

#define ON 1
#define OFF 0

#define PMOSFET_ON 0
#define PMOSFET_OFF 1

#define F_CPU_110592MHZ 1

#ifdef F_CPU_16MHZ
	#define BR115200		16 				/* Baudrate = 115.2K*/
	#define BR9600			207 				/* Baudrate = 9600	*/
	#define F_CPU 16000000UL  // Processor directive, must be defined!
	#define FOSC 16000000UL  // 16 MHz Clock Speed
#endif

#ifdef F_CPU_110592MHZ
	#define BR115200		11 				/* Baudrate = 115.2K*/
	#define BR9600			143 				/* Baudrate = 9600	*/
	#ifndef F_CPU
	#define F_CPU 11059200UL
	#endif
	#define FOSC 11059200UL
#endif

//#define BR115200		16 				/* Baudrate = 115.2K*/
//#define BR9600			207 				/* Baudrate = 9600	*/

#define MSGLNGHT  12
#define CELLNR  10

#define TIME_OUT_PERIOD_MS      100 /*milli seconds [ms]*/

/*--------------
UART: defines
--------------*/
//#define UART_SUCCESS             0x00     /* no error                     */
//#define UART_NO_DATA             0x01     /* no receive data available    */
//#define UART_RX_BUFFER_OVERFLOW  0x02     /* receive ringbuffer overflow  */
//#define UART_TX_BUFFER_OVERFLOW  0x04     /* transmit ringbuffer overflow */
//#define UART_FRAME_ERROR         0x08     /* Framing Error by UART        */
//#define UART_OVERRUN_ERROR       0x10     /* Overrun condition by UART    */
//#define UART_RX_SYNC_ERROR       0x20     /* sync bytes not at beginning  */
//#define UART_NOT_INITIALIZED     0x40     /* module not initialized       */

#define CURSOR_0 lcd_Clear();lcd_SetCursorPos(0, 0)
#define CURSOR_1 lcd_Clear();lcd_SetCursorPos(0, 1)
#define CURSOR_2 lcd_Clear();lcd_SetCursorPos(0, 2)
#define CURSOR_3 lcd_Clear();lcd_SetCursorPos(0, 3)

#define UART_RX_BUFFER_SIZE	512

/* MACRO's */
#define STATUS_CHANGE_CHECK_0 if(mqtt_ptr->Update_Stat_reg != Prev_Status) \
	{console_writeline("\tPrev_Stat_reg 0: 0x%02x", Prev_Status); \
	console_writeline("\tCurrent_Stat_reg 0: 0x%02X", mqtt_ptr->Update_Stat_reg); }

#define STATUS_CHANGE_CHECK_1 if(mqtt_ptr->Update_Stat_reg != Prev_Status) \
	{console_writeline("\tPrev_Stat_reg 1: 0x%02X", Prev_Status); \
	 console_writeline("\tCurrent_Stat_reg 1: 0x%02X", mqtt_ptr->Update_Stat_reg); }


#define HEART_BEAT PinWR(LED_D1, &PORTD, bit) ; bit = ~bit ;


#if !defined NULL
#  define NULL (void*)0
#endif

extern void delay_ms_1(uint16_t ms) ;

#endif // SYSTEM_H
