/*************************************************************************
* Title:    types.h
------------------------------------------------------------------------------
Company         : TXD Systems
Module          : Project generic variable type defines
Purpose         :
Notes           :
History         :
Date			: 24/03/2018
dd/mm/yyyy  Author   Description
----------  ------   -----------
------------------------------------------------------------------------------*/
#ifndef  TYPES_H
	#define TYPES_H

#  include <stdio.h>
#  include <string.h>
#  include <stdint.h>
#  include <stdbool.h>
#  include <avr/io.h>
#  include <avr/interrupt.h>
#  include <avr/eeprom.h>

/*------------------------------------------------------------------------------
                           TYTPE DEFINITIONS
------------------------------------------------------------------------------*/
   /* Standard types */
   typedef uint8_t            UI_8;
   typedef uint16_t           UI_16;
   typedef uint32_t           UI_32;

   typedef uint8_t             SI_8;
   typedef int16_t            SI_16;
   typedef int32_t            SI_32;

   /* Other types */
	typedef UI_8               STATE;
   //typedef void*              HANDLE;
   //typedef void (*VOID_FUNC_PTR)(void);


#define false 0
#define true 1


#endif
