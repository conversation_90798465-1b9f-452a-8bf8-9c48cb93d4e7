/*
 * Timer.h
 *
 * Created: 2018/08/11 02:26:35 PM
 *  Author: Student
 */ 


#ifndef TIMER_H_
#define TIMER_H_

extern void Timer_set_Norm_Mod(void) ;
extern void Timer1_init(uint8_t sec);
extern void Timer1_disable(void);
extern void Timer2_COMPA_init(void) ;
extern void Timer2_RTC_init(void) ;
extern void Timer3_CTC_init(void) ;
extern void Timer3_Norm_init(void) ;
extern uint8_t TIM16_ReadTCNT3( void ) ;
extern void Seconds_update(void) ;
extern  uint8_t Mills(void) ;

#endif /* TIMER_H_ */