/*
 * Timer.h
 *
 * Created: 2018/08/11 02:26:35 PM
 *  Author: Student
 */ 


#ifndef TIMER_H_
#define TIMER_H_

//extern UI_8 intrflags.timer1_intr ;
extern void Timer_set_Norm_Mod(void) ;
extern void Timer1_init(UI_8 sec);
extern void Timer1_disable(void);
extern void Timer2_COMPA_init(void) ;
extern void Timer2_RTC_init(void) ;
extern void Timer3_CTC_init(void) ;
extern void Timer3_Norm_init(void) ;
extern UI_8 TIM16_ReadTCNT3( void ) ;
extern void Seconds_update(void) ;
extern  UI_8 Mills(void) ;

#endif /* TIMER_H_ */

extern volatile uint8_t time_Hour ;
extern volatile uint8_t time_Min ;
//extern volatile uint8_t time_Sec ;
extern volatile UI_16 tick_SEC_old ;