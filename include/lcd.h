/*************************************************************************
* Title:    lcd.h
---------------------------------------------------------------------------
Company         : TXD Systems
Module          : LCD defines
Purpose         :
Notes           :
History         :
Date			: 24/03/2018
dd/mm/yyyy  Author   Description
----------  ------   -----------
------------------------------------------------------------------------------*/
#ifndef LCD_H
#define LCD_H

#include "types.h"

#define LCD_LINE_LENGTH 16

void  lcd_home(void);
bool  lcd_blink_on(void);
bool  lcd_backlight_on(void);
void  lcd_backlight(bool newState);

void lcd_cmd (uint8_t cmd);
void lcd_data(uint8_t data) ;

void lcd_cursor_set_pos(uint8_t x, uint8_t y);
void lcd_cursor_get_pos(uint8_t *x, uint8_t *y);

void lcd_display_string (char *rxdata, uint8_t x, uint8_t y) ;

void lcd_custom_char_init() ;
void lcd_custom_char_display(char ch, uint8_t x, uint8_t y) ;

extern void lcd_init(void);
extern void lcd_clear(void);
extern void lcd_on(bool state);
extern void lcd_write(uint8_t x, uint8_t y, const char *format, ...);
extern void lcd_writeline(uint8_t y, const char *format, ...);

#endif // LCD_H
