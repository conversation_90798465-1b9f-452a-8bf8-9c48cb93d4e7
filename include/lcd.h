/*************************************************************************
* Title:    lcd.h
---------------------------------------------------------------------------
Company         : TXD Systems
Module          : LCD defines
Purpose         :
Notes           :
History         :
Date			: 24/03/2018
dd/mm/yyyy  Author   Description
----------  ------   -----------
------------------------------------------------------------------------------*/
#ifndef LCD_H
#define LCD_H

#include "types.h"

/*------------------------------------------------------------------------------
                        External Functions
------------------------------------------------------------------------------*/
extern void   lcd_Init                (void);
extern void   lcd_Clear               (void);
extern void   lcd_Home                (void);
extern STATE  lcd_BlinkState          (void);
extern void   lcd_BlinkChange         (STATE newState);
extern STATE  lcd_BacklightState      (void);
extern void   lcd_BacklightChange     (STATE newState);

void lcd_on(void);
void lcd_off(void);

extern void lcd_Data(UI_8 data) ;
extern void lcd_Char                (char ch, UI_8 moveCursor);
extern void lcd_Txt                 (char *strPtr, UI_8 moveCursor);
extern void lcd_SetCursorPos        (UI_8 x, UI_8 y);
extern void lcd_GetCursorPos        (UI_8 *x, UI_8 *y);
extern void lcd_display_itoa(char *rxdata, UI_8 val, UI_8 x, UI_8 y, UI_8 d_x);
extern void lcd_display_itoa16(char *rxdata, UI_16 val, UI_8 x, UI_8 y, UI_8 d_x) ;
extern void lcd_dsply_itoa_8bit_sign(char *rxdata, int8_t val, UI_8 x, UI_8 y, UI_8 d_x);
extern void lcd_dsply_int8_sign(int8_t val, UI_8 x, UI_8 y) ;
extern void lcd_display_itoa_signed(char *rxdata, int8_t val, UI_8 x, UI_8 y, UI_8 d_x);

extern void lcd_display_string (char *rxdata, UI_8 x, UI_8 y) ;
extern void lcd_display_char(char *rxdata, char val, UI_8 x, UI_8 y, UI_8 d_x);
extern void Debug_Gothere(UI_8 nr) ;
extern void lcd_display (STATE State) ;
extern void lcd_display_Schar (char val, UI_8 x, UI_8 y) ;

extern void lcd_Custom_Char_Dsply() ;
extern void lcd_Dsply_Custom(char ch, UI_8 x, UI_8 y) ;

extern void LCD_TEST(void) ;

#endif // LCD_H
