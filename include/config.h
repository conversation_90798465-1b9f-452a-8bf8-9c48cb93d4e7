#ifndef CONFIG_H
#define CONFIG_H

#include "types.h"
#include "system.h"

#define CONFIG_IMEI_SIZE 15
#define CONFIG_ICCID_SIZE 20
#define CONFIG_UNITID_SIZE 8
#define CONFIG_REVISION_SIZE 4
#define CONFIG_LOCATION_SIZE 0

#define EEPROM_IMEI_ADDR 0x0000                    // 16 bytes (15 + null terminator)
#define EEPROM_ICCID_ADDR 0x0010                   // 21 bytes (20 + null terminator)  
#define EEPROM_UNITID_ADDR 0x0025                  // 8 bytes (no null terminator needed)
#define EEPROM_REVISION_ADDR 0x002D                // 5 bytes (4 + null terminator)
#define EEPROM_LOCATION_ADDR 0x0032                // 1 byte (0 + null terminator)
#define EEPROM_DOOR_ALARM_MINUTE_ADDR 0x0033       // 1 byte
#define EEPROM_DATA_RESEND_MINUTE_ADDR 0x0034      // 1 byte
#define EEPROM_TRIGGER_RESEND_MINUTE_ADDR 0x0035   // 1 byte
#define EEPROM_DOOR_POLARITY_ADDR 0x0036           // 1 byte
#define EEPROM_TEMP_MIN_ADDR 0x0037                // 2 bytes (int16_t)
#define EEPROM_TEMP_MAX_ADDR 0x0039                // 2 bytes (int16_t)

typedef struct
{
    char imei[CONFIG_IMEI_SIZE + 1];
    char iccid[CONFIG_ICCID_SIZE + 1];
    char unitId[CONFIG_UNITID_SIZE + 1];
    char location[CONFIG_LOCATION_SIZE + 1];
    char revision[CONFIG_REVISION_SIZE + 1];
    uint8_t doorAlarmMinute;
    uint8_t dataResendMinute;
    uint8_t triggerResendMinute;
    uint8_t doorPolarity;
    int16_t tempMin; // 10th of a degree
    int16_t tempMax;
} Config;

extern const Config *config_get(void);
extern const char *config_get_imei(void);
extern void config_set_imei(const char *imei);
extern const char *config_get_iccid(void);
extern void config_set_iccid(const char *iccid);
extern const char *config_get_unit_id(void);
extern const char *config_get_revision(void);
extern const char *config_get_temp_min_string(void);
extern const char *config_get_temp_max_string(void);
extern const char *config_get_string(void);
extern uint8_t config_get_trigger_minutes(void);
extern uint8_t config_get_data_minutes(void);
extern bool config_read_from_eeprom(void);
extern bool config_write_to_eeprom(void);
extern bool config_from_message(char *data);


#endif // CONFIG_H