/*
 * eeprom.h
 *
 * Created: 2019/05/24 08:45:35 PM
 *  Author: <PERSON><PERSON><PERSON>
 */ 


#ifndef EEPROM_H_
#define EEPROM_H_

extern void EEPROM_WR_byte(UI_16 addr, UI_8 data) ;
extern void Lock_EEPROM_WR(void);
extern UI_8 EEPROM_RD_byte(UI_16 addr) ;
extern char EEPROM_RD_Char(UI_16 addr) ;
extern void EEPROM_WR_string(UI_16 addr, char *s, UI_8 lngt) ;
extern void EEPROM_RD_string(UI_16 addr, char *s, uint8_t len) ;
extern UI_8 Check_Phone_Nr(void) ;

#endif /* EEPROM_H_ */