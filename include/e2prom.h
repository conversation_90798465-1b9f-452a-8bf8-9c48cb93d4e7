/*
 * eeprom.h
 *
 * Created: 2019/05/24 08:45:35 PM
 *  Author: <PERSON><PERSON><PERSON>
 */ 

#ifndef EEPROM_H
#define EEPROM_H

extern void EEPROM_WR_byte(uint16_t addr, uint8_t data) ;
extern uint8_t EEPROM_RD_byte(uint16_t addr) ;
extern char EEPROM_RD_Char(uint16_t addr) ;
extern void EEPROM_WR_string(uint16_t addr, char *s, uint8_t lngt) ;
extern bool EEPROM_RD_string(uint16_t addr, char *s, uint8_t len) ;

#endif /* EEPROM_H */