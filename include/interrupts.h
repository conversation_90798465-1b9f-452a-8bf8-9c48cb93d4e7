/*
 * Interrupts.h
 *
 * Created: 2018/10/21 07:44:28 PM
 *  Author: <PERSON><PERSON><PERSON>
 */ 


#ifndef INTERRUPTS_H_
#define INTERRUPTS_H_

/*
 * Bits that are set inside interrupt routines, and watched outside in
 * the program's main loop.
 */
typedef volatile struct
{
	 uint8_t timer1_intr: 1;
	 //uint8_t adc_intr: 1;
	 uint8_t rx_intr: 1;
	 uint8_t UART_LastRxError: 1 ;
}interruptflags ;

extern interruptflags intrflags ;



#endif /* INTERRUPTS_H_ */