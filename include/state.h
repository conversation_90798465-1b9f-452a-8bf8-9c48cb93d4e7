#ifndef STATE_H
#define STATE_H

#include "power.h"
#include "temperature.h"
#include "door.h"

typedef struct 
{
    int16_t temperature;
    Power power;
    bool is_door_open;
    uint8_t signal_strength;
} State;

typedef enum
{
    STATE_DOOR_OPEN = 1 << DOOR_STAT_OC,
    STATE_MAINS_LOW = 1 << VMAIN_LO,
    STATE_BATTERY_LOW = 1 << VBATT_LO,
    STATE_TEMP_LOW = 1 << TEMP_LO,
    STATE_TEMP_HIGH = 1 << TEMP_HI,
    STATE_DOOR_CHANGED = 1 << 5,  // Flag to track door state changes
} StateFlags;

void state_init(int16_t min_temp, int16_t max_temp);
void state_reset(void);
void state_print(void);
void state_update(void);
void state_update_signal_strength(uint8_t signal_strength);
const State *state_get_current(void);

bool state_power_mains_is_low(void);
bool state_power_battery_is_low(void);
bool state_temperatue_is_low(void);
bool state_temperature_is_high(void);
bool state_door_is_open(void);
bool state_door_changed(void);
uint8_t state_get_signal_strength(void);

#endif // STATE_H