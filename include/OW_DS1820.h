/*
 * OW_DS1820.h
 *
 * Created: 2020/05/01 10:30:28 AM
 *  Author: Bronko
 */ 


#ifndef OW_DS1820_H_
#define OW_DS1820_H_

typedef enum
{
    POLARITY_NEGATIVE = 0,
    POLARITY_POSITIVE = 1
} Polarity;

#include "types.h"

 typedef uint8_t byte; // I just like byte & sbyte better
 typedef int8_t sbyte;

extern byte ds1820_Reset(void);
extern byte ds1820_ReadBit(void);
extern  byte ds1820_ReadByte(void);
//extern  void ds1820_GetTemp(char *temp_curr);
//extern void ds1820_GetTemp(mqtt_data_struct *mqtt_dataptr);
extern void RomReaderProgram();
//extern  void ds1820_ReadTempC(byte id[], int8_t *whole, int8_t *decimal);
void ds1820_ReadTempC(int16_t *temperature);
extern  void ds1820_MatchRom(byte rom[]);
extern  void ds1820_ReadTempRaw(byte id[], byte *t0, byte *t1);
extern  void ds1820_WriteBit(byte bit);
extern  void ds1820_WriteByte(byte data);
extern  void DS1820_Test(void);
extern void ds1820_initialise(void);

extern void FindDevices(void) ;

#endif /* OW_DS1820_H_ */