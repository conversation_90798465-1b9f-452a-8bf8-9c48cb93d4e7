/*
 * adc.h
 *
 * Created: 2018/07/08 08:19:33 PM
 *  Author: <PERSON><PERSON><PERSON>
 */ 


#ifndef ADC_H_
#define ADC_H_

 #include "types.h"
/* External variables :
 ----------------------*/
extern UI_8 ADCstarted  ;
extern UI_8 ADCcomplete ;
extern UI_8 ADCvalue ;

/* External functions :
 ----------------------*/
extern void ADC_Init(UI_8 Chan);
extern UI_16 ADC_Meas(UI_8 adc);
extern void ADC_StartConversion(void);


#endif /* ADC_H_ */