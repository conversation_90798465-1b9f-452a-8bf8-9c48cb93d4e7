/*
 * adc.h
 *
 * Created: 2018/07/08 08:19:33 PM
 *  Author: <PERSON><PERSON><PERSON>
 */ 


#ifndef ADC_H_
#define ADC_H_

 #include "types.h"
/* External variables :
 ----------------------*/
extern uint8_t ADCstarted  ;
extern uint8_t ADCcomplete ;
extern uint8_t ADCvalue ;

/* External functions :
 ----------------------*/
extern void adc_init(uint8_t Chan);
extern uint16_t adc_meas(uint8_t adc);
extern void adc_start_conversion(void);


#endif /* ADC_H_ */